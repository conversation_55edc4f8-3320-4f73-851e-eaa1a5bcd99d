/**
 * Video Creation Utilities
 * Shared utility functions for video creation workflows
 */

import { VIDEO_GENERATION_COST } from '@/lib/creditUtils';

/**
 * Form validation utilities
 */
export const formValidation = {
  /**
   * Validate required fields
   */
  validateRequired: (fields, data) => {
    const missing = fields.filter(field => !data[field] || data[field].toString().trim() === '');
    return {
      isValid: missing.length === 0,
      missingFields: missing
    };
  },

  /**
   * Validate text length
   */
  validateTextLength: (text, minLength = 1, maxLength = 5000) => {
    if (!text) return { isValid: false, error: 'Text is required' };
    if (text.length < minLength) return { isValid: false, error: `Text must be at least ${minLength} characters` };
    if (text.length > maxLength) return { isValid: false, error: `Text must be less than ${maxLength} characters` };
    return { isValid: true };
  },

  /**
   * Validate email format
   */
  validateEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return {
      isValid: emailRegex.test(email),
      error: emailRegex.test(email) ? null : 'Invalid email format'
    };
  },

  /**
   * Validate URL format
   */
  validateUrl: (url) => {
    try {
      new URL(url);
      return { isValid: true };
    } catch {
      return { isValid: false, error: 'Invalid URL format' };
    }
  }
};

/**
 * Common form validation for video creation
 */
export function validateVideoCreationForm(formData, requiredFields = []) {
  const errors = {};
  
  // Check required fields
  const { isValid: hasRequired, missingFields } = formValidation.validateRequired(requiredFields, formData);
  if (!hasRequired) {
    missingFields.forEach(field => {
      errors[field] = 'This field is required';
    });
  }

  // Validate project title
  if (formData.projectTitle) {
    const titleValidation = formValidation.validateTextLength(formData.projectTitle, 1, 100);
    if (!titleValidation.isValid) {
      errors.projectTitle = titleValidation.error;
    }
  }

  // Validate script if present
  if (formData.script) {
    const scriptValidation = formValidation.validateTextLength(formData.script, 10, 5000);
    if (!scriptValidation.isValid) {
      errors.script = scriptValidation.error;
    }
  }

  // Validate topic if present
  if (formData.topic) {
    const topicValidation = formValidation.validateTextLength(formData.topic, 3, 200);
    if (!topicValidation.isValid) {
      errors.topic = topicValidation.error;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
}

/**
 * Credit system utilities
 */
export const creditUtils = {
  /**
   * Check if user has sufficient credits
   */
  hasSufficientCredits: (userCredits, requiredCredits = VIDEO_GENERATION_COST) => {
    return userCredits >= requiredCredits;
  },

  /**
   * Calculate remaining credits after operation
   */
  calculateRemainingCredits: (currentCredits, cost = VIDEO_GENERATION_COST) => {
    return Math.max(0, currentCredits - cost);
  },

  /**
   * Format credit display
   */
  formatCredits: (credits) => {
    return `${credits} credit${credits !== 1 ? 's' : ''}`;
  }
};

/**
 * Form data utilities
 */
export const formDataUtils = {
  /**
   * Clean form data by removing empty values
   */
  cleanFormData: (data) => {
    const cleaned = {};
    Object.entries(data).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        cleaned[key] = value;
      }
    });
    return cleaned;
  },

  /**
   * Merge form data with defaults
   */
  mergeWithDefaults: (data, defaults) => {
    return { ...defaults, ...data };
  },

  /**
   * Extract submission data
   */
  getSubmissionData: (formData, requiredFields = []) => {
    const cleaned = formDataUtils.cleanFormData(formData);
    const validation = validateVideoCreationForm(cleaned, requiredFields);
    
    return {
      data: cleaned,
      isValid: validation.isValid,
      errors: validation.errors
    };
  }
};

/**
 * Video type specific configurations
 */
export const videoTypeConfigs = {
  'AI Video': {
    requiredFields: ['projectTitle', 'topic', 'videoStyle', 'voice', 'aspectRatio'],
    creditCost: VIDEO_GENERATION_COST,
    maxScriptLength: 5000,
    supportedAspectRatios: ['16:9', '9:16', '1:1']
  },
  'Meme Video': {
    requiredFields: ['projectTitle', 'videoSource', 'memeText'],
    creditCost: VIDEO_GENERATION_COST,
    maxTextLength: 200,
    supportedAspectRatios: ['16:9', '9:16', '1:1']
  },
  'Podcast Clip': {
    requiredFields: ['projectTitle', 'numberOfClips'],
    creditCost: VIDEO_GENERATION_COST,
    maxClips: 10,
    supportedFormats: ['mp3', 'wav', 'mp4']
  },
  'UGC Video': {
    requiredFields: ['projectTitle', 'selectedCreator'],
    creditCost: VIDEO_GENERATION_COST,
    maxScriptLength: 3000,
    supportedAspectRatios: ['16:9', '9:16']
  }
};

/**
 * Get configuration for video type
 */
export function getVideoTypeConfig(videoType) {
  return videoTypeConfigs[videoType] || videoTypeConfigs['AI Video'];
}

/**
 * Generate preview sections for different video types
 */
export function generatePreviewSections(videoType, formData) {
  const config = getVideoTypeConfig(videoType);
  const sections = [];

  // Common sections
  if (formData.projectTitle) {
    sections.push({
      icon: 'FileText',
      title: 'Project Title',
      content: formData.projectTitle
    });
  }

  // Video type specific sections
  switch (videoType) {
    case 'AI Video':
      if (formData.topic) sections.push({ title: 'Topic', content: formData.topic });
      if (formData.videoStyle) sections.push({ title: 'Style', content: formData.videoStyle });
      if (formData.voice) sections.push({ title: 'Voice', content: formData.voice });
      break;
      
    case 'Meme Video':
      if (formData.memeText) sections.push({ title: 'Meme Text', content: formData.memeText });
      if (formData.font) sections.push({ title: 'Font', content: formData.font });
      break;
      
    case 'Podcast Clip':
      if (formData.numberOfClips) sections.push({ title: 'Number of Clips', content: formData.numberOfClips });
      if (formData.clipDuration) sections.push({ title: 'Clip Duration', content: `${formData.clipDuration}s` });
      break;
      
    case 'UGC Video':
      if (formData.selectedCreator) sections.push({ title: 'Creator', content: formData.selectedCreator.name });
      if (formData.scriptMode) sections.push({ title: 'Script Mode', content: formData.scriptMode });
      break;
  }

  return sections;
}
