"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Plus, Bot, User, Images, Mic, FileText, Film } from "lucide-react";
import Link from "next/link";

const videoCreationOptions = [
  {
    label: "AI Video",
    href: "/dashboard/create-new-short/create-ai-video",
    Icon: <Bot className="w-4 h-4" />,
    description: "Generate video from script"
  },
  {
    label: "AI UGC Video",
    href: "/dashboard/create-new-short/create-ai-ugc-video",
    Icon: <User className="w-4 h-4" />,
    description: "AI avatar with backgrounds"
  },
  {
    label: "Meme Video",
    href: "/dashboard/create-new-short/create-meme-video",
    Icon: <Images className="w-4 h-4" />,
    description: "Create viral memes"
  },
  {
    label: "Podcast Clipper",
    href: "/dashboard/create-new-short/create-podcast-clipper",
    Icon: <Mic className="w-4 h-4" />,
    description: "Extract highlights"
  },
  {
    label: "Reddit Video",
    href: "/dashboard/create-new-short/create-reddit-post-video",
    Icon: <FileText className="w-4 h-4" />,
    description: "From Reddit posts"
  },
  {
    label: "Twitter Video",
    href: "/dashboard/create-new-short/create-twitter-post-video",
    Icon: <FileText className="w-4 h-4" />,
    description: "From Twitter posts"
  },
  {
    label: "Stock Media",
    href: "/dashboard/create-new-short/create-stock-media-video",
    Icon: <Film className="w-4 h-4" />,
    description: "AI with stock content"
  },
  {
    label: "Narrator Video",
    href: "/dashboard/create-new-short/create-new-narrator-short",
    Icon: <FileText className="w-4 h-4" />,
    description: "AI narrative overlay"
  },
  {
    label: "View All Options",
    href: "/dashboard/create-new-short",
    Icon: <Images className="w-4 h-4" />,
    description: "See all creation options"
  }
];

const FloatingActionButton = () => {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="fixed bottom-6 right-6 z-50">
      {/* Main FAB Button */}
      <Button
        onClick={toggleMenu}
        className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200 border-0 relative"
      >
        <motion.div
          animate={{ rotate: isOpen ? 45 : 0 }}
          transition={{
            duration: 0.2,
            ease: "easeInOut",
          }}
        >
          <Plus className="w-7 h-7 text-white" />
        </motion.div>
      </Button>

      {/* Menu Items */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 20 }}
            transition={{
              duration: 0.2,
              ease: "easeOut",
            }}
            className="absolute bottom-20 right-0 flex flex-col items-end gap-2"
          >
            {videoCreationOptions.map((option, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: 20 }}
                transition={{
                  duration: 0.15,
                  delay: index * 0.03,
                  ease: "easeOut",
                }}
              >
                <Link href={option.href} className="block">
                  <Button
                    variant="secondary"
                    size="sm"
                    className="flex items-center gap-2 bg-white/95 hover:bg-white text-gray-900 shadow-lg hover:shadow-xl border border-gray-200/50 rounded-full px-4 py-2 backdrop-blur-sm transition-all duration-200 hover:scale-105 min-w-[140px] justify-start group"
                  >
                    <span className="flex-shrink-0">{option.Icon}</span>
                    <span className="text-sm font-medium truncate">{option.label}</span>
                  </Button>
                </Link>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.15 }}
            className="fixed inset-0 bg-black/30 backdrop-blur-sm -z-10"
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default FloatingActionButton;
