"use client";

import { cn } from "@/lib/utils";
import { Bot, User, Images, Mic, FileText, Film, Video, TrendingUp } from "lucide-react";
import Link from "next/link";

const videoCreationItems = [
  {
    title: "AI Video Generator",
    meta: "Most Popular",
    description: "Create stunning videos from text prompts using advanced AI technology",
    icon: <Bot className="w-4 h-4 text-blue-500" />,
    status: "Live",
    tags: ["AI", "Text-to-Video", "Popular"],
    colSpan: 2,
    hasPersistentHover: true,
    href: "/dashboard/create-new-short/create-ai-video",
    cta: "Create AI Video →"
  },
  {
    title: "AI UGC Creator",
    meta: "New",
    description: "Generate user-generated content style videos with AI avatars",
    icon: <User className="w-4 h-4 text-emerald-500" />,
    status: "Beta",
    tags: ["UGC", "Avatars"],
    href: "/dashboard/create-new-short/create-ai-ugc-video",
    cta: "Try UGC →"
  },
  {
    title: "Meme Generator",
    meta: "Trending",
    description: "Create viral memes and short-form content that engages audiences",
    icon: <Images className="w-4 h-4 text-purple-500" />,
    tags: ["Viral", "Memes"],
    colSpan: 2,
    href: "/dashboard/create-new-short/create-meme-video",
    cta: "Make Memes →"
  },
  {
    title: "Podcast Clipper",
    meta: "Pro Tool",
    description: "Extract highlights and create clips from long-form podcast content",
    icon: <Mic className="w-4 h-4 text-orange-500" />,
    status: "Pro",
    tags: ["Podcast", "Clips"],
    href: "/dashboard/create-new-short/create-podcast-clipper",
    cta: "Clip Audio →"
  },
  {
    title: "Social Media Posts",
    meta: "Multi-platform",
    description: "Transform Reddit and Twitter posts into engaging video content",
    icon: <FileText className="w-4 h-4 text-sky-500" />,
    tags: ["Social", "Reddit", "Twitter"],
    href: "/dashboard/create-new-short/create-reddit-post-video",
    cta: "Convert Posts →"
  },
  {
    title: "Stock Media Videos",
    meta: "Premium",
    description: "Create professional videos using high-quality stock footage",
    icon: <Film className="w-4 h-4 text-red-500" />,
    status: "Premium",
    tags: ["Stock", "Professional"],
    href: "/dashboard/create-new-short/create-stock-media-video",
    cta: "Use Stock →"
  },
  {
    title: "Narrator Videos",
    meta: "Voice AI",
    description: "Add AI-powered narration and voiceovers to your content",
    icon: <Video className="w-4 h-4 text-indigo-500" />,
    tags: ["Voice", "Narration"],
    href: "/dashboard/create-new-short/create-new-narrator-short",
    cta: "Add Voice →"
  }
];

function VideoCreationBentoGrid() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-heading-2">Video Creation Tools</h2>
          <p className="text-body-small text-muted-foreground mt-1">
            Choose from our AI-powered tools to create amazing videos
          </p>
        </div>
        <Link 
          href="/dashboard/create-new-short"
          className="text-sm text-primary hover:text-primary/80 font-medium"
        >
          View All Tools →
        </Link>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-7xl">
        {videoCreationItems.map((item, index) => (
          <Link
            key={index}
            href={item.href}
            className={cn(
              "group relative p-6 rounded-xl overflow-hidden transition-all duration-300",
              "border border-border/50 bg-card hover:bg-card/80",
              "hover:shadow-lg hover:shadow-primary/5 dark:hover:shadow-primary/10",
              "hover:-translate-y-1 will-change-transform cursor-pointer",
              item.colSpan === 2 ? "md:col-span-2" : "col-span-1",
              {
                "shadow-lg -translate-y-1 bg-card/80": item.hasPersistentHover,
              }
            )}
          >
            <div
              className={`absolute inset-0 ${
                item.hasPersistentHover
                  ? "opacity-100"
                  : "opacity-0 group-hover:opacity-100"
              } transition-opacity duration-300`}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-primary/5" />
            </div>

            <div className="relative flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <div className="w-10 h-10 rounded-lg flex items-center justify-center bg-muted group-hover:bg-primary/10 transition-all duration-300">
                  {item.icon}
                </div>
                <span
                  className={cn(
                    "text-xs font-medium px-3 py-1 rounded-full",
                    "bg-muted text-muted-foreground",
                    "transition-colors duration-300 group-hover:bg-primary/10 group-hover:text-primary"
                  )}
                >
                  {item.status || item.meta}
                </span>
              </div>

              <div className="space-y-3">
                <h3 className="font-semibold text-foreground tracking-tight text-lg group-hover:text-primary transition-colors">
                  {item.title}
                </h3>
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {item.description}
                </p>
              </div>

              <div className="flex items-center justify-between mt-4">
                <div className="flex items-center space-x-2">
                  {item.tags?.map((tag, i) => (
                    <span
                      key={i}
                      className="text-xs px-2 py-1 rounded-md bg-muted/50 text-muted-foreground group-hover:bg-primary/10 group-hover:text-primary transition-all duration-200"
                    >
                      #{tag}
                    </span>
                  ))}
                </div>
                <span className="text-xs text-primary opacity-0 group-hover:opacity-100 transition-opacity font-medium">
                  {item.cta}
                </span>
              </div>
            </div>

            <div className="absolute inset-0 -z-10 rounded-xl p-px bg-gradient-to-br from-transparent via-border/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </Link>
        ))}
      </div>
    </div>
  );
}

export default VideoCreationBentoGrid;
