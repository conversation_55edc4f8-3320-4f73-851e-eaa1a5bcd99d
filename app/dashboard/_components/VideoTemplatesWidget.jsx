'use client';

import React from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Sparkles, 
  Bot, 
  User, 
  Images, 
  Mic, 
  FileText, 
  Film,
  Zap,
  Crown,
  TrendingUp,
  Clock
} from 'lucide-react';
import { cn } from '@/lib/utils';

const videoTemplates = [
  {
    id: 'ai-ugc-video',
    name: 'AI UGC Video',
    description: 'AI avatar with your backgrounds',
    href: '/dashboard/create-new-short/create-ai-ugc-video',
    icon: User,
    color: 'bg-emerald-500',
    textColor: 'text-emerald-700',
    bgColor: 'bg-emerald-50',
    borderColor: 'border-emerald-200',
    credits: 15,
    duration: '3-5 min',
    badge: 'New',
    badgeColor: 'bg-emerald-100 text-emerald-700',
    popular: true
  },
  {
    id: 'ai-video',
    name: 'AI Video',
    description: 'Generate from script or topic',
    href: '/dashboard/create-new-short/create-ai-video',
    icon: Bot,
    color: 'bg-blue-500',
    textColor: 'text-blue-700',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    credits: 10,
    duration: '2-4 min',
    badge: 'Popular',
    badgeColor: 'bg-blue-100 text-blue-700',
    popular: true
  },
  {
    id: 'meme-video',
    name: 'Meme Video',
    description: 'Viral content creation',
    href: '/dashboard/create-new-short/create-meme-video',
    icon: Images,
    color: 'bg-purple-500',
    textColor: 'text-purple-700',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    credits: 5,
    duration: '1-2 min',
    badge: 'Quick',
    badgeColor: 'bg-purple-100 text-purple-700',
    popular: true
  },
  {
    id: 'podcast-clipper',
    name: 'Podcast Clipper',
    description: 'Extract highlights',
    href: '/dashboard/create-new-short/create-podcast-clipper',
    icon: Mic,
    color: 'bg-orange-500',
    textColor: 'text-orange-700',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    credits: 8,
    duration: '2-3 min',
    badge: 'Pro',
    badgeColor: 'bg-orange-100 text-orange-700'
  },
  {
    id: 'stock-media',
    name: 'Stock Media',
    description: 'AI with stock content',
    href: '/dashboard/create-new-short/create-stock-media-video',
    icon: Film,
    color: 'bg-teal-500',
    textColor: 'text-teal-700',
    bgColor: 'bg-teal-50',
    borderColor: 'border-teal-200',
    credits: 12,
    duration: '3-4 min',
    badge: 'Premium',
    badgeColor: 'bg-teal-100 text-teal-700'
  },
  {
    id: 'narrator-video',
    name: 'Narrator Video',
    description: 'AI narrative overlay',
    href: '/dashboard/create-new-short/create-new-narrator-short',
    icon: FileText,
    color: 'bg-indigo-500',
    textColor: 'text-indigo-700',
    bgColor: 'bg-indigo-50',
    borderColor: 'border-indigo-200',
    credits: 7,
    duration: '2-3 min',
    badge: 'Classic',
    badgeColor: 'bg-indigo-100 text-indigo-700'
  }
];

function VideoTemplatesWidget() {
  const popularTemplates = videoTemplates.filter(template => template.popular);
  const allTemplates = videoTemplates;

  return (
    <Card className="card-modern">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2 text-heading-3">
              <div className="p-2 bg-gradient-accent rounded-lg">
                <Sparkles className="h-5 w-5 text-white" />
              </div>
              Video Templates
            </CardTitle>
            <CardDescription className="text-body-small mt-1">
              Choose from our AI-powered templates to create amazing videos
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" className="interactive-scale" asChild>
            <Link href="/dashboard/create-new-short">
              View All
            </Link>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Popular Templates */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            <div className="p-1.5 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
              <TrendingUp className="h-4 w-4 text-orange-600 dark:text-orange-400" />
            </div>
            <span className="font-semibold text-body">Most Popular</span>
          </div>

          {popularTemplates.map((template) => {
            const IconComponent = template.icon;
            return (
              <Link
                key={template.id}
                href={template.href}
                className={cn(
                  "block p-5 rounded-xl border-2 transition-all duration-300 hover:shadow-medium group interactive-scale",
                  template.bgColor,
                  template.borderColor,
                  "relative overflow-hidden"
                )}
              >
                <div className="relative z-10">
                  <div className="flex items-center gap-4">
                    <div className={cn("p-3 rounded-xl shadow-soft", template.color)}>
                      <IconComponent className="h-6 w-6 text-white" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h4 className={cn("font-bold text-body", template.textColor)}>
                          {template.name}
                        </h4>
                        <Badge
                          variant="outline"
                          className={cn("text-caption font-medium", template.badgeColor)}
                        >
                          {template.badge}
                        </Badge>
                      </div>
                      <p className="text-body-small text-muted-foreground mb-3">
                        {template.description}
                      </p>
                      <div className="flex items-center gap-4 text-caption text-muted-foreground">
                        <span className="flex items-center gap-1.5">
                          <Zap className="h-3.5 w-3.5" />
                          {template.credits} credits
                        </span>
                        <span className="flex items-center gap-1.5">
                          <Clock className="h-3.5 w-3.5" />
                          {template.duration}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                {/* Background decoration */}
                <div className="absolute top-0 right-0 w-24 h-24 bg-gradient-to-br from-white/10 to-transparent rounded-full blur-xl"></div>
              </Link>
            );
          })}
        </div>

        {/* Quick Access Grid */}
        <div className="pt-6 border-t border-border/50">
          <div className="flex items-center gap-2 mb-4">
            <div className="p-1.5 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg">
              <Crown className="h-4 w-4 text-yellow-600 dark:text-yellow-400" />
            </div>
            <span className="font-semibold text-body">Quick Access</span>
          </div>

          <div className="grid grid-cols-2 gap-3">
            {allTemplates.slice(0, 4).map((template) => {
              const IconComponent = template.icon;
              return (
                <Button
                  key={template.id}
                  variant="outline"
                  size="sm"
                  asChild
                  className="h-auto p-4 flex-col gap-2 interactive-scale hover:bg-accent/50"
                >
                  <Link href={template.href}>
                    <div className={cn("p-2 rounded-lg", template.color)}>
                      <IconComponent className="h-4 w-4 text-white" />
                    </div>
                    <span className="text-caption font-medium">{template.name}</span>
                  </Link>
                </Button>
              );
            })}
          </div>
        </div>

        {/* Pro Tip */}
        <div className="relative overflow-hidden rounded-xl bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/30 dark:to-orange-950/30 p-4 border border-yellow-200/50 dark:border-yellow-800/50">
          <div className="relative z-10">
            <div className="flex items-start gap-3">
              <div className="text-2xl">💡</div>
              <div>
                <p className="font-semibold text-body text-yellow-800 dark:text-yellow-200 mb-1">Pro Tip</p>
                <p className="text-body-small text-yellow-700 dark:text-yellow-300">
                  Start with AI UGC Video for personalized content, or try Meme Video for quick viral content!
                </p>
              </div>
            </div>
          </div>
          <div className="absolute top-0 right-0 w-20 h-20 bg-yellow-400/10 rounded-full blur-xl"></div>
        </div>
      </CardContent>
    </Card>
  );
}

export default VideoTemplatesWidget;
