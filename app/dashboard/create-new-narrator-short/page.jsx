'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { generateNarratorVideo } from '@/actions/narratorVideoGeneration';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { AspectRatio } from '@/components/ui/aspect-ratio';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2 } from 'lucide-react';

export default function CreateNarratorVideoPage() {
  const [videoUrl, setVideoUrl] = useState('');
  const [title, setTitle] = useState('');
  const [aspectRatio, setAspectRatio] = useState('9:16');
  const [templateId, setTemplateId] = useState('NarratorVideo'); // Default to NarratorVideo composition
  const [voice, setVoice] = useState('default'); // Placeholder for voice selection
  const [captionName, setCaptionName] = useState('default'); // Placeholder for caption style name
  const [audioSpeed, setAudioSpeed] = useState(1.0);
  const [backgroundMusic, setBackgroundMusic] = useState('none'); // Placeholder for background music
  const [isLoading, setIsLoading] = useState(false);

  const router = useRouter();

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const result = await generateNarratorVideo({
        videoUrl,
        title,
        aspectRatio,
        templateId,
        voice,
        captionName,
        // For now, send empty objects for styles, these would be configured in a real UI
        captionStyleJson: {}, 
        captionContainerStyle: {},
        audioSpeed,
        backgroundMusic,
      });

      if (result.success) {
        toast.success(result.message || 'Video generation started successfully!');
        // Optionally redirect to a dashboard or video list page
        router.push('/dashboard');
      } else {
        toast.error(result.message || 'Failed to start video generation');
      }
    } catch (error) {
      console.error('Frontend error:', error);
      toast.error('An unexpected error occurred.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Create New Narrator Video</CardTitle>
          <CardDescription>
            Generate a video with AI-analyzed visuals and an on-screen narrative script.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label htmlFor="videoUrl">Original Video URL</Label>
              <Input
                id="videoUrl"
                type="url"
                placeholder="e.g., https://example.com/my-video.mp4"
                value={videoUrl}
                onChange={(e) => setVideoUrl(e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="title">Video Title</Label>
              <Input
                id="title"
                type="text"
                placeholder="Enter a title for your video"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                required
              />
            </div>

            <div>
              <Label htmlFor="aspectRatio">Aspect Ratio</Label>
              <Select value={aspectRatio} onValueChange={setAspectRatio}>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select aspect ratio" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="9:16">9:16 (Portrait)</SelectItem>
                  <SelectItem value="16:9">16:9 (Landscape)</SelectItem>
                  <SelectItem value="1:1">1:1 (Square)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Placeholder for more advanced options like voice, caption styles, background music */}
            <div className="space-y-2">
              <Label>Advanced Options (Placeholders)</Label>
              <Input
                placeholder="Voice (e.g., 'male', 'female')"
                value={voice}
                onChange={(e) => setVoice(e.target.value)}
              />
              <Input
                placeholder="Caption Name (e.g., 'Modern', 'Classic')"
                value={captionName}
                onChange={(e) => setCaptionName(e.target.value)}
              />
              <div>
                <Label htmlFor="audioSpeed">Audio Speed ({audioSpeed.toFixed(1)}x)</Label>
                <Slider
                  id="audioSpeed"
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  value={[audioSpeed]}
                  onValueChange={(val) => setAudioSpeed(val[0])}
                />
              </div>
              <Input
                placeholder="Background Music (e.g., 'upbeat', 'calm')"
                value={backgroundMusic}
                onChange={(e) => setBackgroundMusic(e.target.value)}
              />
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating...
                </>
              ) : (
                'Generate Narrator Video'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
