import React from 'react';
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const MemeStylePresets = ({ onPresetSelect }) => {
  // TODO: Define actual meme style presets

  const presets = [
    {
      name: "Classic Impact",
      font: "Impact",
      fontSize: 56,
      textColor: "#FFFFFF",
      textOutline: true,
      outlineColor: "#000000",
      outlineThickness: 3,
      textShadow: false,
      backgroundColor: null,
      textPosition: "bottom-center",
    },
    {
      name: "Subtle Caption",
      font: "Arial Bold",
      fontSize: 24,
      textColor: "#FFFFFF",
      textOutline: true,
      outlineColor: "#000000",
      outlineThickness: 1,
      textShadow: true,
      backgroundColor: null,
      textPosition: "bottom-center",
    },
    // Add more presets
  ];

  const handlePresetSelect = (presetName) => {
    const selectedPreset = presets.find(preset => preset.name === presetName);
    if (selectedPreset) {
      onPresetSelect(selectedPreset);
    }
  };

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">Meme Style Presets</h3>
       <div>
        <Label htmlFor="presetSelect">Select a Preset</Label>
        <Select onValueChange={handlePresetSelect}>
          <SelectTrigger id="presetSelect">
            <SelectValue placeholder="Choose a preset" />
          </SelectTrigger>
          <SelectContent>
            {presets.map(preset => (
              <SelectItem key={preset.name} value={preset.name}>{preset.name}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default MemeStylePresets;