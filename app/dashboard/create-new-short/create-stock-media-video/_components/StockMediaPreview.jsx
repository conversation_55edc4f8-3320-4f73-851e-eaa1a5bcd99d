import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, Loader2 } from 'lucide-react';

function StockMediaPreview({
  prompt,
  projectTitle,
  videoStyle,
  duration,
  aspectRatio,
  onGenerateVideo,
  isGenerating,
  generationMessage,
  isGenerateButtonDisabled,
  userDetail,
  VIDEO_GENERATION_COST
}) {
  return (
    <div className="relative sticky top-4 xl:top-20 h-fit">
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 dark:from-indigo-950/10 dark:to-purple-950/10 rounded-2xl"></div>
      <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6 max-h-[calc(100vh-2rem)] xl:max-h-[calc(100vh-6rem)] overflow-y-auto">
        {/* Section Header */}
        <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
          <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg shadow-soft">
            <div className="h-6 w-6 text-white flex items-center justify-center text-lg">👁️</div>
          </div>
          <div>
            <h3 className="text-heading-2 text-gradient">Preview & Generate</h3>
            <p className="text-body-small text-muted-foreground">Review your video configuration</p>
          </div>
        </div>

        {/* Video Preview Placeholder */}
        <div className="space-y-4">
          <div className="space-y-3">
            <h4 className="text-body font-medium">Video Preview</h4>
            <div className="bg-gradient-to-br from-muted to-muted/50 rounded-xl overflow-hidden border border-border/50">
              <div className="aspect-video flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-900">
                <div className="text-center p-6">
                  <div className="text-4xl mb-3">🎬</div>
                  <h5 className="text-body font-medium mb-2">Stock Media Video</h5>
                  <p className="text-body-small text-muted-foreground">
                    {prompt ? 'AI will curate stock footage based on your description' : 'Enter a video concept to see preview'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Configuration Summary */}
          <div className="grid grid-cols-1 gap-3">
            <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
              <div className="text-body-small text-muted-foreground space-y-1">
                <div className="flex justify-between">
                  <span>Project:</span>
                  <span className="font-medium text-foreground">{projectTitle || 'Untitled'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Style:</span>
                  <span className="font-medium text-foreground">{videoStyle || 'Not selected'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Duration:</span>
                  <span className="font-medium text-foreground">{duration}s</span>
                </div>
                <div className="flex justify-between">
                  <span>Aspect Ratio:</span>
                  <span className="font-medium text-foreground">{aspectRatio}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Concept Summary */}
          {prompt && (
            <div className="space-y-3">
              <h4 className="text-body font-medium">Video Concept</h4>
              <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
                <p className="text-body-small text-muted-foreground line-clamp-3">
                  {prompt}
                </p>
              </div>
            </div>
          )}

          {/* AI Process Info */}
          <div className="space-y-3">
            <h4 className="text-body font-medium">AI Generation Process</h4>
            <div className="p-3 bg-blue-50/50 dark:bg-blue-950/20 rounded-lg border border-blue-200/30 dark:border-blue-800/30">
              <div className="space-y-2 text-body-small text-blue-800 dark:text-blue-200">
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>Analyze your concept description</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>Search and curate relevant stock footage</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>Generate script and scene timing</span>
                </div>
                <div className="flex items-center gap-2">
                  <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                  <span>Assemble final video with transitions</span>
                </div>
              </div>
            </div>
          </div>

          {/* Generation Status */}
          {isGenerateButtonDisabled && !isGenerating && (
            <div className="p-3 bg-yellow-50/50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200/30 dark:border-yellow-800/30">
              <p className="text-body-small text-yellow-800 dark:text-yellow-200 text-center">
                {!userDetail || userDetail.credits < VIDEO_GENERATION_COST 
                  ? `Insufficient credits (Need ${VIDEO_GENERATION_COST}, have ${userDetail?.credits || 0})`
                  : "Please enter a video concept to generate your stock media video"
                }
              </p>
            </div>
          )}

          {/* Generate Video Button */}
          <div className="pt-4 border-t border-border/30">
            <Button
              onClick={onGenerateVideo}
              disabled={isGenerateButtonDisabled}
              size="lg"
              className="w-full bg-gradient-to-br from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white border-0 shadow-medium interactive-scale h-12"
            >
              {isGenerating ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span className="text-body">{generationMessage}</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  <span className="text-body font-semibold">Generate Stock Media Video</span>
                </div>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default StockMediaPreview;
