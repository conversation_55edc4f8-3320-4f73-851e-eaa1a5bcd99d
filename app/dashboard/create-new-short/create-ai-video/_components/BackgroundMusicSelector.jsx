"use client";

import React, { useState, useRef } from 'react';
import { cn } from '@/lib/utils'; // Import cn utility
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button"; // Import Button
import { Play, Pause, Music2 } from "lucide-react"; // Import icons, added Music2

// TODO: Populate with actual music options and ensure these files exist in public/audio/
const musicOptions = [
  { id: 'none', name: 'None', previewUrl: null },
  { id: 'izzamuzzic', name: 'Izzamuzzic', previewUrl: 'https://cdn.revid.ai/audio/_izzamuzzic.mp3' },
  { id: 'snowfall', name: 'Snowfall', previewUrl: 'https://cdn.revid.ai/audio/_snowfall.mp3' },
  { id: 'roaming-free', name: 'Roaming Free', previewUrl: 'https://cdn.revid.ai/audio/roaming-free.mp3' },
  { id: 'scary-song', name: 'Sc<PERSON> <PERSON>', previewUrl: 'https://cdn.revid.ai/audio/scary_song.mp3' },
  // Keeping existing ones as well, or remove if they are to be fully replaced
  { id: 'observer', name: 'Observer', previewUrl: 'https://cdn.revid.ai/audio/observer.mp3' }, // Renamed id from upbeat-pop
  { id: 'paris-else', name: 'Paris Else', previewUrl: 'https://cdn.revid.ai/audio/_paris-else.mp3' }, // Renamed id from cinematic-orchestral
  { id: 'burlesque', name: 'Burlesque', previewUrl: 'https://cdn.revid.ai/audio/burlesque.mp3' }, // Renamed id from lofi-hiphop
];

function BackgroundMusicSelector({ selectedMusic, onMusicSelect }) {
  const [playingPreview, setPlayingPreview] = useState(null); // Stores the id of the playing preview
  const audioRef = useRef(null);

  const handlePreview = (event, musicId, previewUrl) => {
    event.stopPropagation(); // Prevent select dropdown from closing

    if (audioRef.current) {
      if (playingPreview === musicId) {
        audioRef.current.pause();
        setPlayingPreview(null);
      } else {
        audioRef.current.src = previewUrl;
        audioRef.current.play().catch(error => console.error("Error playing audio:", error));
        setPlayingPreview(musicId);
      }
    }
  };

  // Effect to pause audio when component unmounts or selection changes
  React.useEffect(() => {
    const audioElement = audioRef.current;
    return () => {
      if (audioElement) {
        audioElement.pause();
        audioElement.currentTime = 0;
      }
      setPlayingPreview(null);
    };
  }, []);

  // Pause when a new music is actually selected from the dropdown
  const handleMusicSelection = (value) => {
    if (audioRef.current) {
        audioRef.current.pause();
        setPlayingPreview(null);
    }
    onMusicSelect(value);
  };


  return (
    <div className="space-y-4 rounded-lg border bg-card p-6 shadow-sm">
      <div className="space-y-1">
        <h3 className="text-lg font-semibold">Background Music</h3>
        <p className="text-sm text-muted-foreground">Choose an audio track for your video.</p>
      </div>
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
        {musicOptions.map((music) => {
          const isSelected = selectedMusic === music.id;
          const isPlaying = playingPreview === music.id;
          return (
            <div
              key={music.id}
              onClick={() => handleMusicSelection(music.id)}
              className={cn(
                "group rounded-lg border bg-background p-3 transition-all duration-200 ease-in-out flex flex-col items-center justify-between space-y-2 h-full",
                "cursor-pointer hover:shadow-lg",
                isSelected
                  ? "border-primary ring-2 ring-primary ring-offset-2 ring-offset-background shadow-md"
                  : "hover:border-muted-foreground/60"
              )}
            >
              <div className="flex flex-col items-center text-center flex-grow pt-1">
                {music.id !== 'none'
                  ? <Music2 className={cn("h-7 w-7 mb-2 text-muted-foreground flex-shrink-0", isSelected && "text-primary", "group-hover:text-foreground transition-colors")} />
                  : <div className="h-7 w-7 mb-2 flex-shrink-0"></div>}
                <span className="text-sm font-medium text-foreground truncate w-full">{music.name}</span>
              </div>
              
              {music.previewUrl && (
                <Button
                  variant={isPlaying ? "secondary" : "outline"} // More visible default, distinct playing state
                  size="sm"
                  onClick={(e) => handlePreview(e, music.id, music.previewUrl)}
                  className={cn(
                    "w-full mt-auto text-xs h-8", // Ensure consistent button height
                    // Additional specific styling if needed, e.g., for text color when playing
                    isPlaying && "bg-primary/80 hover:bg-primary/90 text-primary-foreground"
                  )}
                  aria-label={`Preview ${music.name}`}
                >
                  {isPlaying ? <Pause className="h-4 w-4 mr-1.5" /> : <Play className="h-4 w-4 mr-1.5" />}
                  {isPlaying ? 'Pause' : 'Preview'}
                </Button>
              )}
              {music.id === 'none' && (
                <div className="h-8 w-full mt-auto"></div> // Placeholder for consistent height
              )}
            </div>
          );
        })}
      </div>
      <audio ref={audioRef} onEnded={() => setPlayingPreview(null)} />
      {selectedMusic && selectedMusic !== 'none' && (
        <p className="text-sm text-muted-foreground mt-2 text-center">
          Selected: <span className="font-medium text-foreground">{musicOptions.find(m => m.id === selectedMusic)?.name}</span>
        </p>
      )}
    </div>
  );
}

export default BackgroundMusicSelector;