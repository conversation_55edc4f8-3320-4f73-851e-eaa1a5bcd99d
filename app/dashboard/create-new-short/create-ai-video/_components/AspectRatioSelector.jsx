// app/dashboard/create-new/_components/AspectRatioSelector.jsx
'use client';

import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';
import * as LucideIcons from 'lucide-react';
import {
    <PERSON><PERSON><PERSON>,
    Toolt<PERSON>Content,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip";

// Map icon names to Lucide icons to avoid undefined errors
const iconMap = {
    RectangleHorizontal: LucideIcons.RectangleHorizontal,
    AspectRatio: LucideIcons.Ratio,
    RectangleVertical: LucideIcons.RectangleVertical,
};

const aspectRatios = [
    { id: '16:9', name: 'Landscape (16:9)', icon: 'RectangleHorizontal', description: 'Standard widescreen format.', enabled: false },
    { id: '1:1', name: 'Square (1:1)', icon: 'AspectRatio', description: 'Ideal for social media feeds.', enabled: false },
    { id: '9:16', name: 'Vertical (9:16)', icon: 'RectangleVertical', description: 'Perfect for stories and reels.', enabled: true },
];

const defaultEnabledRatio = aspectRatios.find(r => r.enabled)?.id || '';

export default function AspectRatioSelector({ onAspectRatioSelect, selectedAspectRatio: initialSelectedAspectRatio }) {
    const getInitialRatio = () => {
        const initial = aspectRatios.find(r => r.id === initialSelectedAspectRatio);
        if (initial && initial.enabled) {
            return initial.id;
        }
        return defaultEnabledRatio;
    };

    const [selectedRatio, setSelectedRatio] = useState(getInitialRatio());

    useEffect(() => {
        const currentSelected = aspectRatios.find(r => r.id === initialSelectedAspectRatio);
        if (currentSelected && currentSelected.enabled) {
            setSelectedRatio(initialSelectedAspectRatio);
        } else if (initialSelectedAspectRatio && !currentSelected?.enabled) {
            // If an initial aspect ratio was provided but it's now disabled,
            // select the default enabled one and inform the parent.
            setSelectedRatio(defaultEnabledRatio);
            if (onAspectRatioSelect) {
                onAspectRatioSelect(defaultEnabledRatio);
            }
        } else if (!initialSelectedAspectRatio) {
             setSelectedRatio(defaultEnabledRatio);
        }
    }, [initialSelectedAspectRatio, onAspectRatioSelect]);


    const handleSelectRatio = (ratioId) => {
        const ratio = aspectRatios.find(r => r.id === ratioId);
        if (ratio && ratio.enabled) {
            setSelectedRatio(ratioId);
            if (onAspectRatioSelect) {
                onAspectRatioSelect(ratioId);
            }
        }
    };

    return (
        <div className="space-y-4 rounded-lg border bg-card p-6 shadow-sm">
            <h3 className="text-lg font-semibold mb-2">3. Aspect Ratio</h3>
            <p className="text-sm text-muted-foreground mb-4">Choose the dimensions for your video.</p>
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3">
                {aspectRatios.map((ratio) => {
                    const Icon = iconMap[ratio.icon];
                    const isSelected = selectedRatio === ratio.id;

                    const content = (
                        <div
                            onClick={() => handleSelectRatio(ratio.id)}
                            className={cn(
                                "group rounded-lg border bg-background p-4 transition-all duration-200 ease-in-out flex flex-col h-full",
                                ratio.enabled ? "cursor-pointer hover:shadow-md" : "opacity-50 cursor-not-allowed",
                                isSelected && ratio.enabled ? "border-primary ring-2 ring-primary ring-offset-2 ring-offset-background" : (ratio.enabled ? "hover:border-muted-foreground/50" : "border-dashed")
                            )}
                        >
                            <div className="flex flex-col items-center justify-center flex-grow min-w-0">
                                {Icon ? (
                                    <Icon className={cn("h-8 w-8 mb-2 text-muted-foreground flex-shrink-0", ratio.enabled && "group-hover:text-foreground transition-colors")} />
                                ) : (
                                    <div className="h-8 w-8 mb-2 flex-shrink-0" />
                                )}
                                <div className="flex flex-col items-center w-full">
                                    <h4 className="font-medium text-sm mb-1 truncate w-full text-center">{ratio.name}</h4>
                                    <p className="text-xs text-muted-foreground text-center line-clamp-2 break-words">{ratio.description}</p>
                                </div>
                            </div>
                        </div>
                    );

                    if (!ratio.enabled) {
                        return (
                            <TooltipProvider key={ratio.id} delayDuration={300}>
                                <Tooltip>
                                    <TooltipTrigger asChild>{content}</TooltipTrigger>
                                    <TooltipContent>
                                        <p>Coming Soon</p>
                                    </TooltipContent>
                                </Tooltip>
                            </TooltipProvider>
                        );
                    }

                    return <div key={ratio.id}>{content}</div>;
                })}
            </div>
            {selectedRatio && (
                <p className="text-sm text-muted-foreground mt-3">Selected: <span className="font-medium text-foreground">{aspectRatios.find(r => r.id === selectedRatio)?.name}</span></p>
            )}
        </div>
    );
}