// app/dashboard/create-new/_components/VideoStyle.jsx
'use client';

import React, { useState, useEffect } from 'react'; // Added useEffect
import { cn } from '@/lib/utils'; // Assuming you have a utility for class names
import Image from 'next/image'; // Import Image


// --- Placeholder Data --- (Keep as is or update if needed)
const videoStyles = [
    { id: 'cinematic', name: 'Cinematic', description: 'Dramatic, high-quality visuals.', imageUrl: '/app/dashboard/create-new/placeholders/cinamatic.png' },
    { id: 'minimalist', name: 'Minimalist', description: 'Clean lines, simple animations.', imageUrl: '/placeholders/style-minimalist.jpg' },
    { id: 'cartoon', name: 'Cartoon', description: 'Animated, fun, and engaging.', imageUrl: '/placeholders/style-cartoon.jpg' },
    { id: 'documentary', name: 'Documentary', description: 'Informative, realistic style.', imageUrl: '/placeholders/style-documentary.jpg' },
];
// --- End Placeholder ---

// Update props to be specific
// export default function VideoStyle({ onInputChange, formData }) {
export default function VideoStyle({ onStyleSelect, selectedStyle: initialSelectedStyle }) {
    // Use local state synced with the prop
    const [selectedStyle, setSelectedStyle] = useState(initialSelectedStyle || '');

    // Sync local state if the prop changes from the parent
    useEffect(() => {
        setSelectedStyle(initialSelectedStyle || '');
    }, [initialSelectedStyle]);

    const handleSelectStyle = (styleId) => {
        setSelectedStyle(styleId);
        // Call the specific prop handler
        if (onStyleSelect) {
            onStyleSelect(styleId);
        }
    };

    return (
        <div className="space-y-4">
            <div className="mb-4">
                <h3 className="text-body font-semibold mb-1">Video Style</h3>
                <p className="text-body-small text-muted-foreground">Choose the visual aesthetic for your video</p>
            </div>

            <div className="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-4">
                {videoStyles.map((style) => (
                    <div
                        key={style.id}
                        onClick={() => handleSelectStyle(style.id)}
                        className={cn(
                            "group cursor-pointer rounded-lg border bg-background p-3 transition-all duration-200 ease-in-out hover:shadow-sm flex flex-col h-full",
                            selectedStyle === style.id ? "border-primary ring-2 ring-primary ring-offset-1 bg-primary/5" : "border-border/30 hover:border-primary/50"
                        )}
                    >
                        <div className="relative mb-2 aspect-video w-full overflow-hidden rounded bg-muted flex-shrink-0">
                             <Image
                                src={style.imageUrl}
                                alt={`${style.name} style preview`}
                                layout="fill"
                                objectFit="cover"
                                className="transition-transform duration-200 group-hover:scale-105"
                             />
                             {selectedStyle === style.id && (
                                <div className="absolute inset-0 bg-primary/20 flex items-center justify-center">
                                    <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center">
                                        <div className="w-2 h-2 bg-white rounded-full"></div>
                                    </div>
                                </div>
                             )}
                        </div>
                        <div className="flex flex-col flex-grow min-w-0 text-center">
                            <h4 className="font-medium text-body-small mb-1 truncate">{style.name}</h4>
                            <p className="text-caption text-muted-foreground line-clamp-2 break-words">{style.description}</p>
                        </div>
                    </div>
                ))}
            </div>

            {/* Display selected style for confirmation */}
            {selectedStyle && (
                <div className="mt-3 p-2 bg-primary/10 rounded border border-primary/20">
                    <p className="text-body-small text-primary font-medium">
                        ✓ Selected: {videoStyles.find(s => s.id === selectedStyle)?.name}
                    </p>
                </div>
            )}
        </div>
    );
}