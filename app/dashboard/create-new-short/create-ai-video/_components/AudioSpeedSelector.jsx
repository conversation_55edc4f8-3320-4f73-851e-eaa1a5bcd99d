// app/dashboard/create-new/_components/AudioSpeedSelector.jsx
'use client';

import React from 'react';
import { Label } from '@/components/ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';

const speedOptions = [
	{ value: 0.5, label: '0.5x (Very Slow)' },
	{ value: 0.75, label: '0.75x (Slower)' },
	{ value: 1.0, label: '1.0x (Normal)' },
	{ value: 1.25, label: '1.25x (Faster)' },
	{ value: 1.5, label: '1.5x (Very Fast)' },
	{ value: 1.75, label: '1.75x (Even Faster)'},
	{ value: 2.0, label: '2.0x (Fastest)'},
];

export default function AudioSpeedSelector({ selectedSpeed, onSpeedChange }) {
	const handleValueChange = (valueString) => {
		const speedValue = parseFloat(valueString);
		if (onSpeedChange) {
			onSpeedChange(speedValue);
		}
	};

	return (
		<div className="space-y-4 rounded-lg border bg-card p-6 shadow-sm">
			<h3 className="text-lg font-semibold mb-2">Audio Speed</h3>
			<p className="text-sm text-muted-foreground mb-4">
				Adjust the playback speed of the generated audio.
			</p>
			<div className="space-y-2">
				<Label htmlFor="audio-speed-select">Playback Speed</Label>
				<Select
					value={selectedSpeed?.toString() || '1.0'}
					onValueChange={handleValueChange}
					id="audio-speed-select"
				>
					<SelectTrigger className="w-full">
						<SelectValue placeholder="Select speed" />
					</SelectTrigger>
					<SelectContent>
						{speedOptions.map((option) => (
							<SelectItem key={option.value} value={option.value.toString()}>
								{option.label}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>
			{selectedSpeed !== 1.0 && (
				<p className="text-xs text-muted-foreground mt-2">
					Note: Changing audio speed will affect the overall video duration.
					Captions will still sync with the audio.
				</p>
			)}
		</div>
	);
}