'use client';

import React from 'react';
import { Player } from '@remotion/player';
import RedditPostComposition from '@/remotion/compositions/RedditPostComposition'; // Import the component
import { Button } from '@/components/ui/button';
import { Sparkles, Loader2 } from 'lucide-react';

function RedditPostPreview({
  title,
  subreddit,
  username,
  postContent,
  upvotes,
  comments,
  badge,
  darkMode,
  wideLayout,
  approximateCounts,
  hideTrophies,
  hideUpvotes,
  hideComments,
  hideShare,
  avatar,
  redditPostX,
  redditPostY,
  redditPostScale,
  backgroundVideoUrl,
  // Generation props
  onGenerateVideo,
  isGenerating,
  generationMessage,
  isGenerateButtonDisabled,
  userDetail,
  VIDEO_GENERATION_COST
}) {
  // Define video dimensions and duration
  const width = 1080; // Standard vertical video width
  const height = 1920; // Standard vertical video height
  const durationInFrames = 30 * 30; // 30 seconds at 30 frames per second

  return (
    <div className="relative sticky top-4 xl:top-20 h-fit">
      <div className="absolute inset-0 bg-gradient-to-br from-indigo-50/50 to-purple-50/50 dark:from-indigo-950/10 dark:to-purple-950/10 rounded-2xl"></div>
      <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6 max-h-[calc(100vh-2rem)] xl:max-h-[calc(100vh-6rem)] overflow-y-auto">
        {/* Section Header */}
        <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
          <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg shadow-soft">
            <div className="h-6 w-6 text-white flex items-center justify-center text-lg">👁️</div>
          </div>
          <div>
            <h3 className="text-heading-2 text-gradient">Preview & Generate</h3>
            <p className="text-body-small text-muted-foreground">Review your Reddit post video</p>
          </div>
        </div>

        {/* Video Preview */}
        <div className="space-y-4">
          <div className="space-y-3">
            <h4 className="text-body font-medium">Video Preview</h4>
            <div className="bg-gradient-to-br from-muted to-muted/50 rounded-xl overflow-hidden border border-border/50">
              <div style={{ width: '100%', maxWidth: '400px', margin: '0 auto' }}>
                <div style={{ position: 'relative', width: '100%', paddingBottom: `${(height / width) * 100}%` }}>
                  <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}>
                    <Player
                      component={RedditPostComposition}
                      durationInFrames={durationInFrames}
                      fps={30}
                      compositionWidth={width}
                      compositionHeight={height}
                      inputProps={{
                        title,
                        subreddit,
                        username,
                        postContent,
                        upvotes,
                        comments,
                        avatar,
                        badge,
                        darkMode,
                        wideLayout,
                        approximateCounts,
                        hideTrophies,
                        hideUpvotes,
                        hideComments,
                        hideShare,
                        redditPostX,
                        redditPostY,
                        redditPostScale,
                        backgroundVideoUrl,
                      }}
                      loop
                      controls
                      style={{ width: '100%', height: '100%', borderRadius: '8px' }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Configuration Summary */}
          <div className="grid grid-cols-1 gap-3">
            <div className="p-3 bg-muted/20 rounded-lg border border-border/20">
              <div className="text-body-small text-muted-foreground space-y-1">
                <div className="flex justify-between">
                  <span>Subreddit:</span>
                  <span className="font-medium text-foreground">r/{subreddit || 'Not set'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Username:</span>
                  <span className="font-medium text-foreground">{username || 'Not set'}</span>
                </div>
                <div className="flex justify-between">
                  <span>Position:</span>
                  <span className="font-medium text-foreground">{redditPostX}, {redditPostY}</span>
                </div>
                <div className="flex justify-between">
                  <span>Scale:</span>
                  <span className="font-medium text-foreground">{redditPostScale}x</span>
                </div>
              </div>
            </div>
          </div>

          {/* Generation Status */}
          {isGenerateButtonDisabled && !isGenerating && (
            <div className="p-3 bg-yellow-50/50 dark:bg-yellow-950/20 rounded-lg border border-yellow-200/30 dark:border-yellow-800/30">
              <p className="text-body-small text-yellow-800 dark:text-yellow-200 text-center">
                {!userDetail || userDetail.credits < VIDEO_GENERATION_COST
                  ? `Insufficient credits (Need ${VIDEO_GENERATION_COST}, have ${userDetail?.credits || 0})`
                  : "Please fill in all required fields (Title, Content, and at least one Background Video URL or select a default video)."
                }
              </p>
            </div>
          )}

          {/* Generate Video Button */}
          <div className="pt-4 border-t border-border/30">
            <Button
              onClick={onGenerateVideo}
              disabled={isGenerateButtonDisabled}
              size="lg"
              className="w-full bg-gradient-to-br from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600 text-white border-0 shadow-medium interactive-scale h-12"
            >
              {isGenerating ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span className="text-body">{generationMessage}</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5" />
                  <span className="text-body font-semibold">Generate Reddit Video</span>
                </div>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RedditPostPreview;