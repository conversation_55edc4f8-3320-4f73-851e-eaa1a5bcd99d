"use client"

import React, { useState, useEffect, useContext } from 'react';
import { useUser } from "@clerk/nextjs";
import { useRouter } from 'next/navigation';
import { v4 as uuidv4 } from 'uuid';
import { Spark<PERSON>, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { UserDetailContext } from "@/context/UserDetailContext";
import { VIDEO_GENERATION_COST } from '@/lib/creditUtils';

// UI Components
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';

// Local Components
import UserPromptInput from './_components/UserPromptInput';
import NumberOfClipsInput from './_components/NumberOfClipsInput';
import ClippingResultsDisplay from './_components/ClippingResultsDisplay';
import PodcastFileUpload from './_components/PodcastFileUpload';
import YoutubeLinkInput from './_components/YoutubeLinkInput';
import PodcastClipperPreview from './_components/PodcastClipperPreview';
import { processVideo } from '@/actions/generation';


// Server Actions



// --- Main Component ---

const PodcastClipperPage = () => {
  // --- Authentication ---
  const { user, isSignedIn } = useUser();
  const { userDetail, isLoaded } = useContext(UserDetailContext);
  const router = useRouter();

  // --- State Variables ---
  // Project setup
  const [projectTitle, setProjectTitle] = useState('');

  // Input states
  const [youtubeLink, setYoutubeLink] = useState('');
  const [uploadedFileDetails, setUploadedFileDetails] = useState(null); // Stores details { s3Key, uploadedFileId, fileName }
  const [userPrompt, setUserPrompt] = useState('');
  const [numberOfClips, setNumberOfClips] = useState(1); // Default to 1 clip

  // Audio settings
  const [clipDuration, setClipDuration] = useState(60); // Default 60 seconds
  const [audioQuality, setAudioQuality] = useState('high');
  const [includeTranscription, setIncludeTranscription] = useState(true);

  // Loading/Process states
  const [isFileUploading, setIsFileUploading] = useState(false); // State for file upload loading
  const [isClipping, setIsClipping] = useState(false); // State for clipping process loading (after source is ready)
  const [generationMessage, setGenerationMessage] = useState('');


  // Result/Error states
  const [error, setError] = useState(null); // Stores any error messages
  const [results, setResults] = useState(null); // Stores clipping results (expected via webhook/polling)


  // --- Handlers ---

  /**
   * Handles file selection and successful upload from the file upload component.
   * Receives S3 key and uploaded file ID after the file is uploaded to S3.
   * Resets other inputs and state to prepare for clipping.
   * @param {object} fileDetails - Details of the uploaded file.
   * @param {string} fileDetails.s3Key - The S3 key of the uploaded file.
   * @param {string} fileDetails.uploadedFileId - The ID of the uploaded file record.
   * @param {string} fileDetails.fileName - The original name of the uploaded file.
   */
  const handleFileSelect = (fileDetails) => {
    console.log("handleFileSelect: File uploaded and details received:", fileDetails);
    setUploadedFileDetails(fileDetails);
    setYoutubeLink(''); // Clear YouTube link when file is selected
    setError(null); // Clear previous errors
    setResults(null); // Clear previous results
    // Note: isFileUploading should be set to false by handleFileUploadLoadingChange
    console.log("handleFileSelect: State after setting file details:", { uploadedFileDetails: fileDetails, youtubeLink: '', error: null, results: null });
  };


  /**
   * Handles changes to the YouTube link input.
   * Resets other inputs and state to prepare for clipping.
   * @param {object} e - The input change event.
   */
  const handleYoutubeLinkChange = (e) => {
    console.log("handleYoutubeLinkChange: Link changed to:", e.target.value);
    setYoutubeLink(e.target.value);
    setUploadedFileDetails(null); // Clear uploaded file details when link is entered
    setError(null); // Clear previous errors
    setResults(null); // Clear previous results
    console.log("handleYoutubeLinkChange: State after setting link:", { youtubeLink: e.target.value, uploadedFileDetails: null, error: null, results: null });
  };


  /**
   * Handles loading state changes from the file upload component.
   * Updates the component's state to reflect if a file is currently being uploaded.
   * @param {boolean} loading - The current loading state of the file upload.
   */
  const handleFileUploadLoadingChange = (loading) => {
    console.log("handleFileUploadLoadingChange: Loading state changed:", loading);
    setIsFileUploading(loading); // Update file uploading state
    console.log("handleFileUploadLoadingChange: State after setting isFileUploading:", { isFileUploading: loading });
  };


  /**
   * Handles the main "Clip Podcast" button click.
   * Validates input, sets loading state, and triggers an Inngest event
   * to initiate the background clipping process.
   */
  const handleClipPodcast = async () => {
    console.log("handleClipPodcast: Button clicked. Current state:", { youtubeLink, uploadedFileDetails, userPrompt, numberOfClips, isFileUploading, isClipping, isButtonDisabled: (!youtubeLink && !uploadedFileDetails) || isFileUploading || isClipping });

    if ((!youtubeLink && !uploadedFileDetails) || isFileUploading || isClipping) {
      console.log("handleClipPodcast: Button disabled, returning.");
      return;
    }

    if (!user?.id) {
      console.warn("[PodcastClipperPage] handleClipPodcast: User not authenticated.");
      setError("You must be logged in to clip a podcast.");
      toast.error("Please log in to continue.");
      return;
    }

    setIsClipping(true);
    console.log("handleClipPodcast: Setting isClipping to true.");
    setError(null);
    setResults(null);

    try {
      let sourceData = null;
      if (youtubeLink) {
        sourceData = { type: 'youtube', url: youtubeLink };
      } else if (uploadedFileDetails) {
        sourceData = {
          type: 'upload',
          s3Key: uploadedFileDetails.s3Key,
          uploadedFileId: uploadedFileDetails.uploadedFileId,
          fileName: uploadedFileDetails.fileName,
        };
        console.log("handleClipPodcast: Source data:", sourceData);

      }

      if (!sourceData) {
        const errorMessage = "Please provide a YouTube link or upload a file.";
        console.error("handleClipPodcast:", errorMessage);
        setError(errorMessage);
        toast.error(errorMessage);
        setIsClipping(false); // Reset clipping state on validation error
        console.log("handleClipPodcast: Setting isClipping to false due to missing source.");
        return;
      }

      console.log('handleClipPodcast: Triggering Inngest event for podcast clipping...');

      const requestId = uuidv4();

      // Assuming processVideo is the intended function and it's imported elsewhere.
      // NOTE: The signature and return type of processVideo shown in the provided context
      // (actions/generation.js) do NOT match the arguments passed here or the expected
      // return structure ({ success, error, eventId }).
      // This rewrite replaces the function name as requested, assuming processVideo
      // has been or will be modified elsewhere to accept these arguments and return
      // the expected structure. This change will likely cause errors if processVideo
      // is used with the signature shown in the context.
      const { success, error: inngestError, eventId } = await processVideo({
        source: sourceData,
        userPrompt: userPrompt,
        numberOfClips: numberOfClips,
        userId: user.id,
        requestId: requestId,
      });

      if (!success) {
        console.error('handleClipPodcast: Failed to send Inngest event:', inngestError);
        setError(`Failed to start clipping process: ${inngestError}`);
        toast.error(`Failed to start clipping: ${inngestError}`);
        setIsClipping(false); // Reset clipping state on Inngest error
        console.log("handleClipPodcast: Setting isClipping to false due to Inngest error.");
      } else {
        console.log('handleClipPodcast: Inngest event sent successfully with ID:', eventId);
        toast.success('Clipping process started in the background!');
        router.push('/dashboard'); // Redirect to dashboard
        // isClipping remains true to indicate processing is ongoing, awaiting results.
        // The component will need to poll or receive a webhook to set isClipping back to false.
      }

    } catch (err) {
      console.error('handleClipPodcast: Error triggering Inngest event:', err);
      const errorMessage = err.message || 'An unexpected error occurred while starting the clipping process';
      setError(`An error occurred: ${errorMessage}`);
      toast.error(`An error occurred: ${errorMessage}`);
      setIsClipping(false); // Reset clipping state on unexpected error
      console.log("handleClipPodcast: Setting isClipping to false due to unexpected error.");
    } finally {
      // isClipping is managed within the try/catch blocks based on success/failure
      // of sending the event, not reset here. It stays true if the event was sent successfully.
    }
  };


  // --- Derived State / Variables ---
  // Enhanced validation with credit checking
  const isGenerateButtonDisabled = (!youtubeLink && !uploadedFileDetails) ||
                                   isFileUploading ||
                                   isClipping ||
                                   !userDetail ||
                                   userDetail.credits < VIDEO_GENERATION_COST;

  // Determine disabled state for individual inputs
  const isYoutubeInputDisabled = !!uploadedFileDetails || isFileUploading || isClipping;
  const isFileUploadDisabled = !!youtubeLink || !!uploadedFileDetails || isFileUploading || isClipping;
  const isPromptInputDisabled = isFileUploading || isClipping;
  const isNumberOfClipsInputDisabled = isFileUploading || isClipping;

  // Removed console.log as per instruction (assuming the instruction was the log output itself)


  // --- Render ---
  // Only render the page content if the user is signed in
  if (isSignedIn) {
    return (
      <div className="space-y-8 max-w-7xl mx-auto">
        {/* Header Section */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 via-blue-50 to-purple-50 dark:from-purple-950/20 dark:via-blue-950/20 dark:to-purple-950/20 p-8 border border-border/50">
          <div className="relative z-10">
            <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
              <div className="space-y-3">
                <h1 className="text-heading-1 text-gradient">
                  Podcast Clipper 🎙️
                </h1>
                <p className="text-body-large text-muted-foreground max-w-2xl">
                  Transform long-form podcasts into engaging short clips. Extract the best moments with AI-powered content analysis and automatic highlight detection.
                </p>
                <div className="flex items-center gap-4 text-body-small text-muted-foreground">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span>Audio processing ready</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>⚡</span>
                    <span>{userDetail?.credits || 0} credits available</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>🎬</span>
                    <span>Cost: {VIDEO_GENERATION_COST} credits</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Background decoration */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-purple-400/10 to-blue-400/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Left Column - Form Sections */}
          <div className="xl:col-span-2 space-y-6">

            {/* Section 1: Project Setup */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-50/50 to-blue-50/50 dark:from-purple-950/10 dark:to-blue-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">📝</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Project Setup</h2>
                    <p className="text-body-small text-muted-foreground">Configure your podcast clipping project</p>
                  </div>
                </div>

                {/* Project Title */}
                <div className="space-y-3">
                  <Label htmlFor="projectTitle" className="text-body font-medium">
                    Project Title <span className="text-body-small text-muted-foreground">(Optional)</span>
                  </Label>
                  <Input
                    type="text"
                    id="projectTitle"
                    value={projectTitle}
                    onChange={(e) => setProjectTitle(e.target.value)}
                    placeholder="e.g., My Podcast Highlights"
                    className="w-full h-12 px-4 rounded-lg border border-border bg-background text-body focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors"
                    disabled={isClipping}
                  />
                </div>
              </div>
            </div>

            {/* Section Connector */}
            <div className="flex justify-center">
              <div className="w-px h-8 bg-gradient-to-b from-purple-300 to-emerald-300 dark:from-purple-600 dark:to-emerald-600"></div>
            </div>

            {/* Section 2: Content Source */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-emerald-50/50 to-purple-50/50 dark:from-emerald-950/10 dark:to-purple-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-emerald-500 to-purple-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">🎙️</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Content Source</h2>
                    <p className="text-body-small text-muted-foreground">Choose your podcast source - YouTube link or upload file</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* YouTube Link Input */}
                  <div className="space-y-3">
                    <h3 className="text-body font-semibold">YouTube Link</h3>
                    <YoutubeLinkInput
                      value={youtubeLink}
                      onChange={handleYoutubeLinkChange}
                      disabled={isYoutubeInputDisabled}
                    />
                  </div>

                  {/* OR Divider */}
                  <div className="flex items-center w-full">
                    <div className="flex-grow border-t border-border/30"></div>
                    <span className="px-3 text-muted-foreground text-body-small font-semibold">OR</span>
                    <div className="flex-grow border-t border-border/30"></div>
                  </div>

                  {/* File Upload Component */}
                  <div className="space-y-3">
                    <h3 className="text-body font-semibold">Upload Audio/Video File</h3>
                    <PodcastFileUpload
                      onFileSelect={handleFileSelect}
                      disabled={isFileUploadDisabled}
                      onLoadingChange={handleFileUploadLoadingChange}
                    />
                    {/* Display selected file name if a file has been uploaded */}
                    {uploadedFileDetails && (
                      <div className="p-3 bg-green-50/50 dark:bg-green-950/20 rounded-lg border border-green-200/30 dark:border-green-800/30">
                        <p className="text-body-small text-green-800 dark:text-green-200">
                          Selected file: <span className="font-medium">{uploadedFileDetails.fileName}</span>
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Section Connector */}
            <div className="flex justify-center">
              <div className="w-px h-8 bg-gradient-to-b from-emerald-300 to-blue-300 dark:from-emerald-600 dark:to-blue-600"></div>
            </div>

            {/* Section 3: Content Creation */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-cyan-50/50 dark:from-blue-950/10 dark:to-cyan-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg shadow-soft">
                    <Sparkles className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Content Creation</h2>
                    <p className="text-body-small text-muted-foreground">Define what type of clips you want to extract</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* User Prompt Input */}
                  <div className="space-y-3">
                    <UserPromptInput
                      value={userPrompt}
                      onChange={(e) => setUserPrompt(e.target.value)}
                      disabled={isPromptInputDisabled}
                    />
                  </div>

                  {/* Number of Clips Input */}
                  <div className="space-y-3">
                    <NumberOfClipsInput
                      value={numberOfClips}
                      onChange={setNumberOfClips}
                      disabled={isNumberOfClipsInputDisabled}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Section Connector */}
            <div className="flex justify-center">
              <div className="w-px h-8 bg-gradient-to-b from-blue-300 to-orange-300 dark:from-blue-600 dark:to-orange-600"></div>
            </div>

            {/* Section 4: Audio Settings */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-orange-50/50 to-red-50/50 dark:from-orange-950/10 dark:to-red-950/10 rounded-2xl"></div>
              <div className="relative bg-background/80 backdrop-blur-sm border border-border/50 rounded-2xl p-6">
                {/* Section Header */}
                <div className="flex items-center gap-3 mb-6 pb-4 border-b border-border/30">
                  <div className="p-2 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg shadow-soft">
                    <div className="h-6 w-6 text-white flex items-center justify-center text-lg">🔊</div>
                  </div>
                  <div>
                    <h2 className="text-heading-2 text-gradient">Audio Settings</h2>
                    <p className="text-body-small text-muted-foreground">Configure audio processing and output options</p>
                  </div>
                </div>

                <div className="space-y-6">
                  {/* Clip Duration */}
                  <div className="space-y-3">
                    <Label htmlFor="clipDuration" className="text-body font-medium">Maximum Clip Duration (seconds)</Label>
                    <Input
                      id="clipDuration"
                      type="number"
                      value={clipDuration}
                      onChange={(e) => setClipDuration(parseInt(e.target.value) || 60)}
                      min="15"
                      max="300"
                      className="h-10"
                      disabled={isClipping}
                    />
                  </div>

                  {/* Audio Quality */}
                  <div className="space-y-3">
                    <Label htmlFor="audioQuality" className="text-body font-medium">Audio Quality</Label>
                    <select
                      id="audioQuality"
                      value={audioQuality}
                      onChange={(e) => setAudioQuality(e.target.value)}
                      className="w-full h-10 px-3 rounded-lg border border-border bg-background text-body focus:border-primary focus:ring-2 focus:ring-primary/20 transition-colors"
                      disabled={isClipping}
                    >
                      <option value="high">High Quality</option>
                      <option value="medium">Medium Quality</option>
                      <option value="low">Low Quality</option>
                    </select>
                  </div>

                  {/* Include Transcription */}
                  <div className="flex items-center justify-between">
                    <Label htmlFor="includeTranscription" className="text-body font-medium">Include Transcription</Label>
                    <input
                      id="includeTranscription"
                      type="checkbox"
                      checked={includeTranscription}
                      onChange={(e) => setIncludeTranscription(e.target.checked)}
                      className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                      disabled={isClipping}
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Preview */}
          <div className="xl:col-span-1">
            <PodcastClipperPreview
              projectTitle={projectTitle}
              youtubeLink={youtubeLink}
              uploadedFileDetails={uploadedFileDetails}
              userPrompt={userPrompt}
              numberOfClips={numberOfClips}
              clipDuration={clipDuration}
              audioQuality={audioQuality}
              includeTranscription={includeTranscription}
              onClipPodcast={handleClipPodcast}
              isClipping={isClipping}
              isFileUploading={isFileUploading}
              generationMessage={generationMessage}
              isGenerateButtonDisabled={isGenerateButtonDisabled}
              userDetail={userDetail}
              VIDEO_GENERATION_COST={VIDEO_GENERATION_COST}
              error={error}
              results={results}
            />
          </div>
        </div>
      </div>
    );
  }

  // Show loading or sign-in prompt if not authenticated
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Please sign in to create podcast clips</h1>
        <p className="text-muted-foreground">You need to be signed in to access the podcast clipping tools.</p>
      </div>
    </div>
  );
};

export default PodcastClipperPage;
