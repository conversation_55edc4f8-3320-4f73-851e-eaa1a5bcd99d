/**
 * AI Video Server Actions
 * 
 * Server-side actions for AI video generation workflow
 * including script generation, validation, and error handling.
 */

'use server';

import { generateScriptFromAI } from '@/src/lib/aiUtils';
import { withCircuitBreaker } from '@/lib/circuitBreaker';

/**
 * Generate AI video script with circuit breaker protection
 */
export async function aiVideoScriptGeneration(input) {
  try {
    // Input validation
    if (!input || typeof input !== 'object') {
      throw new Error('Invalid input: input must be an object');
    }

    const { topic, videoStyle, userId } = input;

    // Validate required fields
    if (!topic || typeof topic !== 'string' || topic.trim().length === 0) {
      throw new Error('Topic is required');
    }

    if (!videoStyle || typeof videoStyle !== 'string' || videoStyle.trim().length === 0) {
      throw new Error('Video style is required');
    }

    // Sanitize input
    const sanitizedTopic = sanitizeInput(topic);
    const sanitizedVideoStyle = sanitizeInput(videoStyle);

    // Validate topic length
    if (sanitizedTopic.length > 500) {
      throw new Error('Topic too long');
    }

    // Prepare parameters for AI generation
    const aiParams = {
      topic: sanitizedTopic,
      style: sanitizedVideoStyle,
      maxWords: 200
    };

    console.log('[AI Video Script] Generating script with params:', aiParams);

    // Generate script with circuit breaker protection
    const result = await withCircuitBreaker(
      'GOOGLE_AI',
      async () => {
        return await generateScriptFromAI(aiParams);
      },
      async () => {
        // Fallback script generation
        console.log('[AI Video Script] Using fallback script generation');
        return generateFallbackScript(sanitizedTopic, sanitizedVideoStyle);
      }
    );

    console.log('[AI Video Script] Script generation completed successfully');

    return {
      success: true,
      script: result.script,
      metadata: result.metadata || { wordCount: result.script?.length || 0 }
    };

  } catch (error) {
    console.error('[AI Video Script] Error generating script:', error);

    // Return structured error response
    return {
      success: false,
      error: getErrorMessage(error)
    };
  }
}

/**
 * Sanitize user input to prevent XSS and other security issues
 */
function sanitizeInput(input) {
  if (typeof input !== 'string') {
    return '';
  }

  // Remove HTML tags and script content
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
    .trim();
}

/**
 * Generate fallback script when AI service is unavailable
 */
async function generateFallbackScript(topic, style) {
  console.log('[AI Video Script] Generating fallback script for topic:', topic);

  // Simple template-based fallback
  const templates = {
    'Professional': `Let's explore the fascinating topic of ${topic}. This subject has gained significant attention recently, and for good reason. Understanding ${topic} can provide valuable insights and practical applications in our daily lives.`,
    'Casual': `Hey everyone! Today we're talking about ${topic}. This is something that's been on my mind lately, and I think you'll find it really interesting. Let's dive into what makes ${topic} so important.`,
    'Educational': `Welcome to today's lesson on ${topic}. We'll be covering the key concepts, important details, and practical applications. By the end of this video, you'll have a solid understanding of ${topic}.`,
    'Entertaining': `Get ready for an exciting journey into the world of ${topic}! We're about to discover some amazing facts and insights that will change how you think about this subject.`
  };

  const template = templates[style] || templates['Professional'];
  
  return {
    script: template,
    metadata: {
      source: 'fallback',
      wordCount: template.split(' ').length,
      estimatedDuration: Math.ceil(template.split(' ').length / 2.5) // ~2.5 words per second
    }
  };
}

/**
 * Get user-friendly error message
 */
function getErrorMessage(error) {
  const errorMessage = error.message || 'Unknown error';

  // Map technical errors to user-friendly messages
  const errorMappings = {
    'API_RATE_LIMIT_EXCEEDED': 'Rate limit exceeded. Please try again in a few minutes.',
    'INVALID_API_KEY': 'Authentication failed. Please contact support.',
    'SERVICE_UNAVAILABLE': 'Service temporarily unavailable. Please try again later.',
    'Topic is required': 'Please enter a topic for your video.',
    'Video style is required': 'Please select a video style.',
    'Topic too long': 'Please shorten your topic to 500 characters or less.'
  };

  // Check for specific error patterns
  for (const [pattern, message] of Object.entries(errorMappings)) {
    if (errorMessage.includes(pattern)) {
      return message;
    }
  }

  // Default error message
  return 'An error occurred while generating the script. Please try again.';
}

/**
 * Validate AI video generation input
 */
export async function validateAIVideoInput(formData) {
  const errors = [];

  // Validate project title
  if (!formData.projectTitle || formData.projectTitle.trim().length === 0) {
    errors.push('Project title is required');
  } else if (formData.projectTitle.length > 100) {
    errors.push('Project title must be 100 characters or less');
  }

  // Validate topic
  if (!formData.topic || formData.topic.trim().length === 0) {
    errors.push('Topic is required');
  } else if (formData.topic.length > 500) {
    errors.push('Topic must be 500 characters or less');
  }

  // Validate video style
  const validStyles = ['Professional', 'Casual', 'Educational', 'Entertaining'];
  if (!formData.videoStyle || !validStyles.includes(formData.videoStyle)) {
    errors.push('Please select a valid video style');
  }

  // Validate aspect ratio
  const validAspectRatios = ['16:9', '9:16', '1:1'];
  if (formData.aspectRatio && !validAspectRatios.includes(formData.aspectRatio)) {
    errors.push('Please select a valid aspect ratio');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Get AI video generation cost estimate
 */
export async function getAIVideoCostEstimate(formData) {
  try {
    const { getCostForWorkflow } = await import('@/lib/atomicCreditSystem');
    
    const cost = getCostForWorkflow('AI_VIDEO', formData);
    
    return {
      success: true,
      cost,
      breakdown: {
        baseCost: 5,
        durationAdjustment: cost - 5,
        totalCost: cost
      }
    };
  } catch (error) {
    console.error('[AI Video Cost] Error calculating cost:', error);
    
    return {
      success: false,
      error: 'Unable to calculate cost estimate'
    };
  }
}
