/**
 * Authentication Guard Hook
 * Handles authentication checks and redirects for protected pages
 */

import { useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';

/**
 * @param {Object} options
 * @param {string} options.redirectTo - Where to redirect unauthenticated users
 * @param {boolean} options.requireAuth - Whether authentication is required
 * @param {Function} options.onAuthChange - Callback when auth state changes
 */
export function useAuthGuard({
  redirectTo = '/sign-in',
  requireAuth = true,
  onAuthChange
} = {}) {
  const { isLoaded, isSignedIn, user } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (isLoaded && requireAuth && !isSignedIn) {
      router.push(redirectTo);
    }
    
    if (onAuthChange) {
      onAuthChange({ isLoaded, isSignedIn, user });
    }
  }, [isLoaded, isSignedIn, user, requireAuth, redirectTo, router, onAuthChange]);

  return {
    isLoaded,
    isSignedIn,
    user,
    isAuthenticated: isLoaded && isSignedIn,
    isLoading: !isLoaded
  };
}

/**
 * Hook for pages that require authentication
 */
export function useRequireAuth(redirectTo = '/sign-in') {
  return useAuthGuard({ requireAuth: true, redirectTo });
}

/**
 * Hook for pages that work with optional authentication
 */
export function useOptionalAuth() {
  return useAuthGuard({ requireAuth: false });
}
