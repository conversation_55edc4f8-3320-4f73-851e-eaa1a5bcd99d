/**
 * AI Video Form Hook - Migrated to use universal video generation
 * Combines the existing useAIVideoForm with our new universal patterns
 */

import { useState, useCallback, useMemo } from 'react';
import { useVideoGeneration } from './useVideoGeneration';
import { triggerAIVideoGeneration } from '@/actions/aiVideoGeneration';
import { validateVideoCreationForm, getVideoTypeConfig } from '@/lib/videoCreationUtils';

const initialFormData = {
  projectTitle: '',
  topic: '',
  videoStyle: '',
  aspectRatio: '',
  script: '',
  voice: '',
  audioSpeed: 1.0,
  backgroundMusic: '',
  caption: '',
  templateId: ''
};

/**
 * AI Video Form Hook
 * Manages form state, validation, and video generation for AI videos
 */
export function useAIVideoForm() {
  const [formData, setFormData] = useState(initialFormData);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Get AI Video configuration
  const config = getVideoTypeConfig('AI Video');

  // Form validation function
  const validateForm = useCallback((data = formData) => {
    const validation = validateVideoCreationForm(data, config.requiredFields);
    setErrors(validation.errors);
    return validation.isValid;
  }, [formData, config.requiredFields]);

  // Video generation hook
  const videoGeneration = useVideoGeneration({
    generationAction: triggerAIVideoGeneration,
    videoType: 'AI Video',
    creditCost: config.creditCost,
    validateForm
  });

  /**
   * Update a single form field
   */
  const updateField = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
    
    // Mark field as touched
    setTouched(prev => ({ ...prev, [field]: true }));
  }, [errors]);

  /**
   * Update multiple form fields
   */
  const updateFields = useCallback((updates) => {
    setFormData(prev => ({ ...prev, ...updates }));
    
    // Clear errors for updated fields
    const updatedFields = Object.keys(updates);
    if (updatedFields.some(field => errors[field])) {
      setErrors(prev => {
        const newErrors = { ...prev };
        updatedFields.forEach(field => {
          if (newErrors[field]) {
            delete newErrors[field];
          }
        });
        return newErrors;
      });
    }
    
    // Mark updated fields as touched
    setTouched(prev => ({
      ...prev,
      ...Object.keys(updates).reduce((acc, field) => ({ ...acc, [field]: true }), {})
    }));
  }, [errors]);

  /**
   * Handle field blur for validation
   */
  const handleFieldBlur = useCallback((field) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    
    // Validate single field
    const value = formData[field];
    const fieldConfig = config.requiredFields.includes(field);
    
    if (fieldConfig && (!value || (typeof value === 'string' && !value.trim()))) {
      setErrors(prev => ({ ...prev, [field]: 'This field is required' }));
    } else if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  }, [formData, errors, config.requiredFields]);

  /**
   * Get error for specific field
   */
  const getFieldError = useCallback((field) => {
    return touched[field] ? errors[field] : null;
  }, [errors, touched]);

  /**
   * Reset form to initial state
   */
  const resetForm = useCallback(() => {
    setFormData(initialFormData);
    setErrors({});
    setTouched({});
  }, []);

  /**
   * Get submission data with validation
   */
  const getSubmissionData = useCallback(() => {
    const isValid = validateForm();
    return {
      data: formData,
      isValid,
      errors: isValid ? {} : errors
    };
  }, [formData, validateForm, errors]);

  /**
   * Check if form is valid
   */
  const isValid = useMemo(() => {
    return config.requiredFields.every(field => {
      const value = formData[field];
      return value && (typeof value !== 'string' || value.trim());
    });
  }, [formData, config.requiredFields]);

  /**
   * Check if form has errors
   */
  const hasErrors = useMemo(() => {
    return Object.keys(errors).length > 0;
  }, [errors]);

  /**
   * Calculate estimated duration based on script length
   */
  const estimatedDuration = useMemo(() => {
    if (!formData.script) return 0;
    // Rough estimate: 150 words per minute, average 5 characters per word
    const wordCount = formData.script.length / 5;
    const minutes = wordCount / 150;
    return Math.max(15, Math.round(minutes * 60)); // Minimum 15 seconds
  }, [formData.script]);

  /**
   * Enhanced generate preview that updates script field
   */
  const generatePreview = useCallback(async (topic, videoStyle) => {
    const script = await videoGeneration.generatePreview(topic, videoStyle);
    if (script) {
      updateField('script', script);
    }
    return script;
  }, [videoGeneration.generatePreview, updateField]);

  return {
    // Form data
    formData,
    errors,
    touched,
    
    // Computed properties
    isValid,
    hasErrors,
    estimatedDuration,
    
    // Actions
    updateField,
    updateFields,
    validateForm,
    handleFieldBlur,
    getFieldError,
    resetForm,
    getSubmissionData,
    
    // Video generation (enhanced)
    videoGeneration: {
      ...videoGeneration,
      generatePreview // Override with our enhanced version
    },
    
    // Individual field accessors for convenience
    projectTitle: formData.projectTitle,
    topic: formData.topic,
    videoStyle: formData.videoStyle,
    aspectRatio: formData.aspectRatio,
    script: formData.script,
    voice: formData.voice,
    audioSpeed: formData.audioSpeed,
    backgroundMusic: formData.backgroundMusic,
    caption: formData.caption,
    templateId: formData.templateId
  };
}
