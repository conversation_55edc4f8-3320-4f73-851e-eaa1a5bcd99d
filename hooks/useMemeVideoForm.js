/**
 * Meme Video Form Hook
 * Demonstrates reusability of universal video generation patterns
 */

import { useState, useCallback, useMemo } from 'react';
import { useVideoGeneration } from './useVideoGeneration';
import { triggerMemeVideoGeneration } from '@/actions/memeVideoGeneration';
import { validateVideoCreationForm, getVideoTypeConfig } from '@/lib/videoCreationUtils';

const initialFormData = {
  projectTitle: '',
  videoSource: '',
  memeText: '',
  font: 'Impact',
  fontSize: 48,
  textColor: '#FFFFFF',
  textOutline: true,
  outlineColor: '#000000',
  outlineThickness: 2,
  textShadow: false,
  backgroundColor: 'transparent',
  textPosition: 'center',
  aspectRatio: '16:9',
  videoStartTime: 0,
  videoEndTime: 10,
  useOriginalAudio: true,
  backgroundMusic: '',
  originalAudioVolume: 100,
  backgroundMusicVolume: 50,
  templateId: 'MemeVideoTemplate'
};

/**
 * Meme Video Form Hook
 * Manages form state, validation, and video generation for meme videos
 */
export function useMemeVideoForm() {
  const [formData, setFormData] = useState(initialFormData);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});

  // Get Meme Video configuration
  const config = getVideoTypeConfig('Meme Video');

  // Form validation function
  const validateForm = useCallback((data = formData) => {
    const validation = validateVideoCreationForm(data, config.requiredFields);
    
    // Add meme-specific validation
    const memeErrors = { ...validation.errors };
    
    // Validate meme text length
    if (data.memeText && data.memeText.length > config.maxTextLength) {
      memeErrors.memeText = `Text must be less than ${config.maxTextLength} characters`;
    }
    
    // Validate video source
    if (!data.videoSource || data.videoSource.trim() === '') {
      memeErrors.videoSource = 'Video source is required';
    }
    
    setErrors(memeErrors);
    return Object.keys(memeErrors).length === 0;
  }, [formData, config]);

  // Video generation hook
  const videoGeneration = useVideoGeneration({
    generationAction: triggerMemeVideoGeneration,
    videoType: 'Meme Video',
    creditCost: config.creditCost,
    validateForm
  });

  /**
   * Update a single form field
   */
  const updateField = useCallback((field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when field is updated
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
    
    // Mark field as touched
    setTouched(prev => ({ ...prev, [field]: true }));
  }, [errors]);

  /**
   * Update multiple form fields
   */
  const updateFields = useCallback((updates) => {
    setFormData(prev => ({ ...prev, ...updates }));
    
    // Clear errors for updated fields
    const updatedFields = Object.keys(updates);
    if (updatedFields.some(field => errors[field])) {
      setErrors(prev => {
        const newErrors = { ...prev };
        updatedFields.forEach(field => {
          if (newErrors[field]) {
            delete newErrors[field];
          }
        });
        return newErrors;
      });
    }
    
    // Mark updated fields as touched
    setTouched(prev => ({
      ...prev,
      ...Object.keys(updates).reduce((acc, field) => ({ ...acc, [field]: true }), {})
    }));
  }, [errors]);

  /**
   * Handle field blur for validation
   */
  const handleFieldBlur = useCallback((field) => {
    setTouched(prev => ({ ...prev, [field]: true }));
    
    // Validate single field
    const value = formData[field];
    const fieldConfig = config.requiredFields.includes(field);
    
    if (fieldConfig && (!value || (typeof value === 'string' && !value.trim()))) {
      setErrors(prev => ({ ...prev, [field]: 'This field is required' }));
    } else if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  }, [formData, errors, config.requiredFields]);

  /**
   * Get error for specific field
   */
  const getFieldError = useCallback((field) => {
    return touched[field] ? errors[field] : null;
  }, [errors, touched]);

  /**
   * Reset form to initial state
   */
  const resetForm = useCallback(() => {
    setFormData(initialFormData);
    setErrors({});
    setTouched({});
  }, []);

  /**
   * Get submission data with validation
   */
  const getSubmissionData = useCallback(() => {
    const isValid = validateForm();
    return {
      data: formData,
      isValid,
      errors: isValid ? {} : errors
    };
  }, [formData, validateForm, errors]);

  /**
   * Check if form is valid
   */
  const isValid = useMemo(() => {
    return config.requiredFields.every(field => {
      const value = formData[field];
      return value && (typeof value !== 'string' || value.trim());
    });
  }, [formData, config.requiredFields]);

  /**
   * Check if form has errors
   */
  const hasErrors = useMemo(() => {
    return Object.keys(errors).length > 0;
  }, [errors]);

  /**
   * Calculate estimated duration based on video timing
   */
  const estimatedDuration = useMemo(() => {
    const start = formData.videoStartTime || 0;
    const end = formData.videoEndTime || 10;
    return Math.max(1, end - start);
  }, [formData.videoStartTime, formData.videoEndTime]);

  return {
    // Form data
    formData,
    errors,
    touched,
    
    // Computed properties
    isValid,
    hasErrors,
    estimatedDuration,
    
    // Actions
    updateField,
    updateFields,
    validateForm,
    handleFieldBlur,
    getFieldError,
    resetForm,
    getSubmissionData,
    
    // Video generation
    videoGeneration,
    
    // Individual field accessors for convenience
    projectTitle: formData.projectTitle,
    videoSource: formData.videoSource,
    memeText: formData.memeText,
    font: formData.font,
    fontSize: formData.fontSize,
    textColor: formData.textColor,
    textOutline: formData.textOutline,
    outlineColor: formData.outlineColor,
    outlineThickness: formData.outlineThickness,
    textShadow: formData.textShadow,
    backgroundColor: formData.backgroundColor,
    textPosition: formData.textPosition,
    aspectRatio: formData.aspectRatio,
    videoStartTime: formData.videoStartTime,
    videoEndTime: formData.videoEndTime,
    useOriginalAudio: formData.useOriginalAudio,
    backgroundMusic: formData.backgroundMusic,
    originalAudioVolume: formData.originalAudioVolume,
    backgroundMusicVolume: formData.backgroundMusicVolume,
    templateId: formData.templateId
  };
}
