/**
 * Universal Video Generation Hook
 * Handles video generation operations for all video types
 */

import { useState, useCallback, useContext } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { UserDetailContext } from '@/context/UserDetailContext';
import { VIDEO_GENERATION_COST } from '@/lib/creditUtils';

/**
 * @param {Object} config
 * @param {Function} config.generationAction - Server action for video generation
 * @param {string} config.videoType - Type of video being generated
 * @param {number} config.creditCost - Cost in credits for generation
 * @param {Function} config.validateForm - Form validation function
 */
export function useVideoGeneration({
  generationAction,
  videoType = 'video',
  creditCost = VIDEO_GENERATION_COST,
  validateForm = () => true,
} = {}) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationMessage, setGenerationMessage] = useState('');
  const [previewData, setPreviewData] = useState(null);
  
  const { userDetail, setUserDetail, invalidateVideoCache } = useContext(UserDetailContext);
  const router = useRouter();

  /**
   * Check if user has sufficient credits
   */
  const hasSufficientCredits = useCallback(() => {
    return userDetail && userDetail.credits >= creditCost;
  }, [userDetail, creditCost]);

  /**
   * Generate preview (for applicable video types)
   */
  const generatePreview = useCallback(async (formData) => {
    if (!validateForm(formData)) {
      toast.error('Please complete all required fields');
      return null;
    }

    setIsGenerating(true);
    setGenerationMessage('Generating preview...');

    try {
      // This would be customized per video type
      // For now, return the form data as preview
      setPreviewData(formData);
      toast.success('Preview generated successfully');
      return formData;
    } catch (error) {
      console.error('[Preview] Error:', error);
      toast.error('Failed to generate preview');
      return null;
    } finally {
      setIsGenerating(false);
      setGenerationMessage('');
    }
  }, [validateForm]);

  /**
   * Generate full video
   */
  const generateVideo = useCallback(async (formData) => {
    // Validation checks
    if (!userDetail) {
      toast.error('User information not loaded');
      return false;
    }

    if (!hasSufficientCredits()) {
      toast.error('Insufficient Credits', {
        description: `You need ${creditCost} credits to generate a ${videoType}. You have ${userDetail.credits} credits.`
      });
      return false;
    }

    if (!validateForm(formData)) {
      toast.error('Please complete all required fields');
      return false;
    }

    if (!generationAction) {
      toast.error('Generation action not configured');
      return false;
    }

    setIsGenerating(true);
    setGenerationMessage(`Generating ${videoType}...`);

    try {
      const result = await generationAction(formData);
      
      if (result.success) {
        console.log(`[Generation] ${videoType} workflow triggered successfully:`, result.eventId);
        
        toast.success('Generation Started!', {
          description: `Your ${videoType} is being generated in the background. Check your dashboard for updates.`
        });
        
        // Optimistically update credits in UI
        setUserDetail(prev => prev ? { 
          ...prev, 
          credits: Math.max(0, prev.credits - creditCost) 
        } : null);
        
        // Invalidate video cache since a new video is being created
        if (invalidateVideoCache) {
          invalidateVideoCache();
        }
        
        // Redirect to dashboard after triggering
        router.push('/dashboard');
        
        return true;
      } else {
        console.error(`[Generation] Failed to trigger ${videoType} workflow:`, result.error);
        throw new Error(result.error || `Failed to start ${videoType} generation`);
      }
    } catch (error) {
      console.error(`[Generation] ${videoType} generation error:`, error);
      toast.error('Generation Failed', {
        description: error.message || `An error occurred while generating your ${videoType}.`
      });
      return false;
    } finally {
      setIsGenerating(false);
      setGenerationMessage('');
    }
  }, [userDetail, hasSufficientCredits, validateForm, generationAction, videoType, creditCost, setUserDetail, invalidateVideoCache, router]);

  /**
   * Clear preview data
   */
  const clearPreview = useCallback(() => {
    setPreviewData(null);
  }, []);

  /**
   * Check if generate button should be disabled
   */
  const isGenerateButtonDisabled = useCallback((formData) => {
    return isGenerating || !hasSufficientCredits() || !validateForm(formData);
  }, [isGenerating, hasSufficientCredits, validateForm]);

  /**
   * Get generation button text
   */
  const getGenerationButtonText = useCallback(() => {
    if (isGenerating) return generationMessage;
    return `Generate ${videoType.charAt(0).toUpperCase() + videoType.slice(1)}`;
  }, [isGenerating, generationMessage, videoType]);

  /**
   * Get generation button title (tooltip)
   */
  const getGenerationButtonTitle = useCallback((formData) => {
    if (!hasSufficientCredits()) {
      return `Insufficient credits (Need ${creditCost})`;
    }
    if (!validateForm(formData)) {
      return "Please complete all required fields";
    }
    if (isGenerating) {
      return generationMessage;
    }
    return `Generate ${videoType}`;
  }, [hasSufficientCredits, validateForm, isGenerating, generationMessage, videoType, creditCost]);

  return {
    // State
    isGenerating,
    generationMessage,
    previewData,
    
    // Actions
    generatePreview,
    generateVideo,
    clearPreview,
    
    // Computed properties
    isGenerateButtonDisabled,
    getGenerationButtonText,
    getGenerationButtonTitle,
    hasSufficientCredits,
    
    // Constants
    creditCost,
    videoType
  };
}
