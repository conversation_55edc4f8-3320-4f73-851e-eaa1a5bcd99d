/**
 * FormField Molecule
 * Combines label, input, and validation message into a cohesive form field
 */

import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { cn } from '@/lib/utils';

/**
 * Base FormField component
 */
function FormField({
  label,
  error,
  required = false,
  className,
  children,
  description,
  ...props
}) {
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <Label className={cn(
          "text-sm font-medium",
          required && "after:content-['*'] after:ml-0.5 after:text-destructive"
        )}>
          {label}
        </Label>
      )}
      
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
      
      {children}
      
      {error && (
        <p className="text-sm text-destructive">{error}</p>
      )}
    </div>
  );
}

/**
 * Input FormField
 */
export function InputFormField({
  label,
  error,
  required,
  description,
  className,
  inputClassName,
  ...inputProps
}) {
  return (
    <FormField
      label={label}
      error={error}
      required={required}
      description={description}
      className={className}
    >
      <Input
        className={cn(
          error && "border-destructive focus-visible:ring-destructive",
          inputClassName
        )}
        {...inputProps}
      />
    </FormField>
  );
}

/**
 * Textarea FormField
 */
export function TextareaFormField({
  label,
  error,
  required,
  description,
  className,
  textareaClassName,
  ...textareaProps
}) {
  return (
    <FormField
      label={label}
      error={error}
      required={required}
      description={description}
      className={className}
    >
      <Textarea
        className={cn(
          error && "border-destructive focus-visible:ring-destructive",
          textareaClassName
        )}
        {...textareaProps}
      />
    </FormField>
  );
}

/**
 * Select FormField
 */
export function SelectFormField({
  label,
  error,
  required,
  description,
  className,
  selectClassName,
  placeholder = "Select an option...",
  options = [],
  value,
  onValueChange,
  ...selectProps
}) {
  return (
    <FormField
      label={label}
      error={error}
      required={required}
      description={description}
      className={className}
    >
      <Select value={value} onValueChange={onValueChange} {...selectProps}>
        <SelectTrigger className={cn(
          error && "border-destructive focus-visible:ring-destructive",
          selectClassName
        )}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          {options.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </FormField>
  );
}

export default FormField;
