/**
 * Meme Video Configuration Form Organism
 * Demonstrates reusability of atomic components across different video types
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { InputFormField, TextareaFormField, SelectFormField } from '@/components/molecules/forms/FormField';
import { Button } from '@/components/ui/button';
import { Smile, Upload, Palette, Settings } from 'lucide-react';

// Import existing meme-specific components
import VideoInput from '@/app/dashboard/create-new-short/create-meme-video/_components/VideoInput';
import TextStylingOptions from '@/app/dashboard/create-new-short/create-meme-video/_components/TextStylingOptions';
import PositioningOptions from '@/app/dashboard/create-new-short/create-meme-video/_components/PositioningOptions';
import AISuggestions from '@/app/dashboard/create-new-short/create-meme-video/_components/AISuggestions';
import MemeStylePresets from '@/app/dashboard/create-new-short/create-meme-video/_components/MemeStylePresets';
import AspectRatioControl from '@/app/dashboard/create-new-short/create-meme-video/_components/AspectRatioControl';
import AudioOptions from '@/app/dashboard/create-new-short/create-meme-video/_components/AudioOptions';

/**
 * @param {Object} props
 * @param {Object} props.formData - Current form data
 * @param {Function} props.onFieldChange - Field change handler
 * @param {Object} props.errors - Form validation errors
 * @param {Object} props.videoGeneration - Video generation hook data
 */
export default function MemeVideoConfigurationForm({
  formData = {},
  onFieldChange,
  errors = {},
  videoGeneration = {},
  ...props
}) {
  const handleFieldChange = (field, value) => {
    if (onFieldChange) {
      onFieldChange(field, value);
    }
  };

  const handlePresetSelect = (preset) => {
    // Apply multiple preset values at once
    const presetUpdates = {
      font: preset.font,
      fontSize: preset.fontSize,
      textColor: preset.textColor,
      textOutline: preset.textOutline,
      outlineColor: preset.outlineColor,
      outlineThickness: preset.outlineThickness,
      textShadow: preset.textShadow,
      backgroundColor: preset.backgroundColor,
      textPosition: preset.textPosition
    };

    Object.entries(presetUpdates).forEach(([field, value]) => {
      handleFieldChange(field, value);
    });
  };

  return (
    <div className="space-y-6">
      {/* Project Details Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Smile className="h-5 w-5 text-primary" />
            Project Details
          </CardTitle>
          <CardDescription>
            Set up your meme video project
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <InputFormField
            label="Project Title"
            placeholder="Enter your meme project title..."
            value={formData.projectTitle || ''}
            onChange={(e) => handleFieldChange('projectTitle', e.target.value)}
            error={errors.projectTitle}
            required
          />
        </CardContent>
      </Card>

      {/* Video Source Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5 text-primary" />
            Video Source
          </CardTitle>
          <CardDescription>
            Upload or provide the source video for your meme
          </CardDescription>
        </CardHeader>
        <CardContent>
          <VideoInput
            videoSource={formData.videoSource}
            onVideoSourceChange={(source) => handleFieldChange('videoSource', source)}
            videoStartTime={formData.videoStartTime}
            onVideoStartTimeChange={(time) => handleFieldChange('videoStartTime', time)}
            videoEndTime={formData.videoEndTime}
            onVideoEndTimeChange={(time) => handleFieldChange('videoEndTime', time)}
          />
          {errors.videoSource && (
            <p className="text-sm text-destructive mt-2">{errors.videoSource}</p>
          )}
        </CardContent>
      </Card>

      {/* Meme Text Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Palette className="h-5 w-5 text-primary" />
            Meme Text & Styling
          </CardTitle>
          <CardDescription>
            Add text and customize its appearance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Meme Text Input */}
          <TextareaFormField
            label="Meme Text"
            placeholder="Enter your meme text..."
            value={formData.memeText || ''}
            onChange={(e) => handleFieldChange('memeText', e.target.value)}
            error={errors.memeText}
            rows={3}
            description={`${(formData.memeText || '').length}/200 characters`}
            required
          />

          <Separator />

          {/* AI Suggestions */}
          <div className="space-y-2">
            <label className="text-sm font-medium">AI Text Suggestions</label>
            <AISuggestions 
              onTextSuggest={(text) => handleFieldChange('memeText', text)}
            />
          </div>

          <Separator />

          {/* Style Presets */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Quick Style Presets</label>
            <MemeStylePresets onPresetSelect={handlePresetSelect} />
          </div>

          <Separator />

          {/* Text Styling Options */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Custom Text Styling</label>
            <TextStylingOptions
              font={formData.font}
              onFontChange={(font) => handleFieldChange('font', font)}
              fontSize={formData.fontSize}
              onFontSizeChange={(size) => handleFieldChange('fontSize', size)}
              textColor={formData.textColor}
              onTextColorChange={(color) => handleFieldChange('textColor', color)}
              textOutline={formData.textOutline}
              onTextOutlineChange={(outline) => handleFieldChange('textOutline', outline)}
              outlineColor={formData.outlineColor}
              onOutlineColorChange={(color) => handleFieldChange('outlineColor', color)}
              outlineThickness={formData.outlineThickness}
              onOutlineThicknessChange={(thickness) => handleFieldChange('outlineThickness', thickness)}
              textShadow={formData.textShadow}
              onTextShadowChange={(shadow) => handleFieldChange('textShadow', shadow)}
              backgroundColor={formData.backgroundColor}
              onBackgroundColorChange={(color) => handleFieldChange('backgroundColor', color)}
            />
          </div>

          <Separator />

          {/* Text Positioning */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Text Position</label>
            <PositioningOptions
              textPosition={formData.textPosition}
              onTextPositionChange={(position) => handleFieldChange('textPosition', position)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Video Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5 text-primary" />
            Video Configuration
          </CardTitle>
          <CardDescription>
            Configure aspect ratio and audio settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Aspect Ratio */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Aspect Ratio</label>
            <AspectRatioControl
              aspectRatio={formData.aspectRatio}
              onAspectRatioChange={(ratio) => handleFieldChange('aspectRatio', ratio)}
            />
          </div>

          <Separator />

          {/* Audio Options */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Audio Settings</label>
            <AudioOptions
              useOriginalAudio={formData.useOriginalAudio}
              onUseOriginalAudioChange={(use) => handleFieldChange('useOriginalAudio', use)}
              backgroundMusic={formData.backgroundMusic}
              onBackgroundMusicChange={(music) => handleFieldChange('backgroundMusic', music)}
              originalAudioVolume={formData.originalAudioVolume}
              onOriginalAudioVolumeChange={(volume) => handleFieldChange('originalAudioVolume', volume)}
              backgroundMusicVolume={formData.backgroundMusicVolume}
              onBackgroundMusicVolumeChange={(volume) => handleFieldChange('backgroundMusicVolume', volume)}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
