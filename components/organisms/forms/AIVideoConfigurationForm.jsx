/**
 * AI Video Configuration Form Organism
 * Migrated to use Atomic Design components
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { InputFormField, TextareaFormField, SelectFormField } from '@/components/molecules/forms/FormField';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON><PERSON><PERSON>, Wand2 } from 'lucide-react';

// Import existing specialized components that we'll keep for now
import VideoStyle from '@/app/dashboard/create-new-short/create-ai-video/_components/VideoStyle';
import Voice from '@/app/dashboard/create-new-short/create-ai-video/_components/Voice';
import Captions from '@/app/dashboard/create-new-short/create-ai-video/_components/Captions';
import AspectRatioSelector from '@/app/dashboard/create-new-short/create-ai-video/_components/AspectRatioSelector';
import TemplateSelector from '@/app/dashboard/create-new-short/create-ai-video/_components/TemplateSelector';
import AudioSpeedSelector from '@/app/dashboard/create-new-short/create-ai-video/_components/AudioSpeedSelector';
import BackgroundMusicSelector from '@/app/dashboard/create-new-short/create-ai-video/_components/BackgroundMusicSelector';

/**
 * @param {Object} props
 * @param {Object} props.formData - Current form data
 * @param {Function} props.onFieldChange - Field change handler
 * @param {Object} props.errors - Form validation errors
 * @param {Object} props.videoGeneration - Video generation hook data
 */
export default function AIVideoConfigurationForm({
  formData = {},
  onFieldChange,
  errors = {},
  videoGeneration = {},
  ...props
}) {
  const {
    generatePreview,
    isGenerating,
    previewScript,
    clearPreview
  } = videoGeneration;

  const handleFieldChange = (field, value) => {
    if (onFieldChange) {
      onFieldChange(field, value);
    }
  };

  const handleGeneratePreview = async () => {
    if (generatePreview) {
      const script = await generatePreview(formData.topic, formData.videoStyle);
      if (script) {
        handleFieldChange('script', script);
      }
    }
  };

  return (
    <div className="space-y-6">
      {/* Project Details Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-primary" />
            Project Details
          </CardTitle>
          <CardDescription>
            Set up your video project with a title and topic
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <InputFormField
            label="Project Title"
            placeholder="Enter your project title..."
            value={formData.projectTitle || ''}
            onChange={(e) => handleFieldChange('projectTitle', e.target.value)}
            error={errors.projectTitle}
            required
          />
          
          <InputFormField
            label="Video Topic"
            placeholder="What should your video be about?"
            value={formData.topic || ''}
            onChange={(e) => handleFieldChange('topic', e.target.value)}
            error={errors.topic}
            description="Describe the main topic or theme for your AI-generated video"
            required
          />
        </CardContent>
      </Card>

      {/* Video Style & Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Video Style & Configuration</CardTitle>
          <CardDescription>
            Choose the style and format for your video
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Video Style Selector */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Video Style *</label>
            <VideoStyle
              selectedStyle={formData.videoStyle}
              onStyleChange={(style) => handleFieldChange('videoStyle', style)}
            />
            {errors.videoStyle && (
              <p className="text-sm text-destructive">{errors.videoStyle}</p>
            )}
          </div>

          <Separator />

          {/* Aspect Ratio */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Aspect Ratio *</label>
            <AspectRatioSelector
              selectedRatio={formData.aspectRatio}
              onRatioChange={(ratio) => handleFieldChange('aspectRatio', ratio)}
            />
            {errors.aspectRatio && (
              <p className="text-sm text-destructive">{errors.aspectRatio}</p>
            )}
          </div>

          <Separator />

          {/* Template Selector */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Template *</label>
            <TemplateSelector
              selectedTemplate={formData.templateId}
              onTemplateChange={(template) => handleFieldChange('templateId', template)}
            />
            {errors.templateId && (
              <p className="text-sm text-destructive">{errors.templateId}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Script Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Wand2 className="h-5 w-5 text-primary" />
            Script Content
          </CardTitle>
          <CardDescription>
            Generate an AI script or write your own
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Generate Preview Button */}
          <div className="flex gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleGeneratePreview}
              disabled={!formData.topic || !formData.videoStyle || isGenerating}
              className="flex-1"
            >
              <Sparkles className="mr-2 h-4 w-4" />
              {isGenerating ? 'Generating...' : 'Generate AI Script'}
            </Button>
            
            {previewScript && (
              <Button
                type="button"
                variant="ghost"
                onClick={clearPreview}
              >
                Clear
              </Button>
            )}
          </div>

          {/* Script Textarea */}
          <TextareaFormField
            label="Video Script"
            placeholder="Your video script will appear here, or write your own..."
            value={formData.script || ''}
            onChange={(e) => handleFieldChange('script', e.target.value)}
            error={errors.script}
            rows={6}
            description={`${(formData.script || '').length}/5000 characters`}
            required
          />
        </CardContent>
      </Card>

      {/* Audio Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Audio Configuration</CardTitle>
          <CardDescription>
            Configure voice, speed, and background music
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Voice Selector */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Voice *</label>
            <Voice
              selectedVoice={formData.voice}
              onVoiceChange={(voice) => handleFieldChange('voice', voice)}
            />
            {errors.voice && (
              <p className="text-sm text-destructive">{errors.voice}</p>
            )}
          </div>

          <Separator />

          {/* Audio Speed */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Audio Speed</label>
            <AudioSpeedSelector
              selectedSpeed={formData.audioSpeed}
              onSpeedChange={(speed) => handleFieldChange('audioSpeed', speed)}
            />
          </div>

          <Separator />

          {/* Background Music */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Background Music *</label>
            <BackgroundMusicSelector
              selectedMusic={formData.backgroundMusic}
              onMusicChange={(music) => handleFieldChange('backgroundMusic', music)}
            />
            {errors.backgroundMusic && (
              <p className="text-sm text-destructive">{errors.backgroundMusic}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Captions */}
      <Card>
        <CardHeader>
          <CardTitle>Captions & Subtitles</CardTitle>
          <CardDescription>
            Configure how captions appear in your video
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Captions
            selectedCaption={formData.caption}
            onCaptionChange={(caption) => handleFieldChange('caption', caption)}
          />
        </CardContent>
      </Card>
    </div>
  );
}
