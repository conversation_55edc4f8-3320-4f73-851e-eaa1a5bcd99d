/**
 * VideoPreviewPanel Organism
 * Universal preview panel for all video creation workflows
 */

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Video, Clock, FileText, User, Settings, AlertCircle, CheckCircle } from 'lucide-react';
import GenerationButton from '@/components/atoms/buttons/GenerationButton';
import CreditBadge from '@/components/atoms/indicators/CreditBadge';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {Object} props.formData - Current form data
 * @param {Object} props.videoGeneration - Video generation hook data
 * @param {string} props.videoType - Type of video
 * @param {Array} props.previewSections - Sections to show in preview
 * @param {Function} props.onGenerate - Generation handler
 * @param {string} props.className - Additional CSS classes
 */
export default function VideoPreviewPanel({
  formData = {},
  videoGeneration = {},
  videoType = 'Video',
  previewSections = [],
  onGenerate,
  className,
  children,
  ...props
}) {
  const {
    isGenerating = false,
    generationMessage = '',
    hasSufficientCredits = () => true,
    creditCost = 0,
    isGenerateButtonDisabled = () => false,
    getGenerationButtonText = () => `Generate ${videoType}`,
    getGenerationButtonTitle = () => '',
    generateVideo
  } = videoGeneration;

  const handleGenerate = onGenerate || generateVideo;

  return (
    <div className={cn("space-y-6", className)} {...props}>
      {/* Preview Card */}
      <Card className="relative overflow-hidden">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Video className="h-5 w-5 text-primary" />
              <CardTitle className="text-lg">Preview & Generate</CardTitle>
            </div>
            <CreditBadge 
              credits={formData.userCredits || 0}
              requiredCredits={creditCost}
            />
          </div>
          <CardDescription>
            Review your configuration and generate your {videoType.toLowerCase()}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Preview Sections */}
          {previewSections.length > 0 && (
            <div className="space-y-4">
              {previewSections.map((section, index) => (
                <div key={index}>
                  <PreviewSection {...section} />
                  {index < previewSections.length - 1 && <Separator className="mt-4" />}
                </div>
              ))}
            </div>
          )}

          {/* Default Preview Content */}
          {previewSections.length === 0 && (
            <div className="space-y-4">
              {formData.projectTitle && (
                <PreviewSection
                  icon={FileText}
                  title="Project Title"
                  content={formData.projectTitle}
                />
              )}
              
              {formData.script && (
                <PreviewSection
                  icon={FileText}
                  title="Script"
                  content={formData.script}
                  truncate={true}
                />
              )}
              
              {formData.voice && (
                <PreviewSection
                  icon={User}
                  title="Voice"
                  content={formData.voice}
                />
              )}
              
              {formData.aspectRatio && (
                <PreviewSection
                  icon={Settings}
                  title="Aspect Ratio"
                  content={formData.aspectRatio}
                />
              )}
            </div>
          )}

          {/* Credit Status Alert */}
          {!hasSufficientCredits() && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Insufficient credits. You need {creditCost} credits to generate this {videoType.toLowerCase()}.
              </AlertDescription>
            </Alert>
          )}

          {/* Generation Button */}
          <GenerationButton
            isGenerating={isGenerating}
            disabled={isGenerateButtonDisabled(formData)}
            generationMessage={generationMessage}
            defaultText={getGenerationButtonText()}
            title={getGenerationButtonTitle(formData)}
            onClick={() => handleGenerate(formData)}
            className="w-full"
          />

          {/* Additional Content */}
          {children}
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Preview Section Component
 */
function PreviewSection({ 
  icon: Icon = FileText, 
  title, 
  content, 
  badge,
  truncate = false,
  className 
}) {
  if (!content) return null;

  const displayContent = truncate && content.length > 100 
    ? `${content.substring(0, 100)}...` 
    : content;

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium">{title}</span>
        </div>
        {badge && <Badge variant="secondary" className="text-xs">{badge}</Badge>}
      </div>
      <p className="text-sm text-muted-foreground pl-6">
        {displayContent}
      </p>
    </div>
  );
}

export { PreviewSection };
