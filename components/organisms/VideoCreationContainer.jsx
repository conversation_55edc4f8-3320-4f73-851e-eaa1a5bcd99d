/**
 * VideoCreationContainer Organism
 * Universal container for all video creation workflows
 */

import React from 'react';
import { useRequireAuth } from '@/hooks/useAuthGuard';
import { useVideoGeneration } from '@/hooks/useVideoGeneration';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.title - Page title
 * @param {string} props.description - Page description
 * @param {string} props.videoType - Type of video being created
 * @param {Function} props.generationAction - Server action for generation
 * @param {Function} props.validateForm - Form validation function
 * @param {React.ReactNode} props.configurationPanel - Left panel content
 * @param {React.ReactNode} props.previewPanel - Right panel content
 * @param {Object} props.formData - Current form data
 * @param {string} props.className - Additional CSS classes
 * @param {number} props.creditCost - Cost in credits
 */
export default function VideoCreationContainer({
  title,
  description,
  videoType,
  generationAction,
  validateForm,
  configurationPanel,
  previewPanel,
  formData = {},
  className,
  creditCost,
  children,
  ...props
}) {
  // Authentication guard
  const { isAuthenticated, isLoading } = useRequireAuth();

  // Video generation logic
  const videoGeneration = useVideoGeneration({
    generationAction,
    videoType,
    creditCost,
    validateForm: (data) => validateForm ? validateForm(data) : true
  });

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated (redirect will happen)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className={cn("space-y-8 max-w-7xl mx-auto", className)} {...props}>
      {/* Header Section */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-50 via-blue-50 to-purple-50 dark:from-purple-950/20 dark:via-blue-950/20 dark:to-purple-950/20 p-8 border border-border/50">
        <div className="relative z-10">
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
            <div className="space-y-3">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-purple-600 via-blue-600 to-purple-600 bg-clip-text text-transparent">
                {title}
              </h1>
              <p className="text-lg text-muted-foreground max-w-2xl">
                {description}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        {/* Configuration Panel */}
        <div className="xl:col-span-2 space-y-6">
          {React.cloneElement(configurationPanel, {
            formData,
            videoGeneration,
            ...configurationPanel.props
          })}
        </div>

        {/* Preview Panel */}
        <div className="xl:col-span-1">
          {React.cloneElement(previewPanel, {
            formData,
            videoGeneration,
            ...previewPanel.props
          })}
        </div>
      </div>

      {/* Additional Content */}
      {children}
    </div>
  );
}

/**
 * Specialized video creation containers
 */

export function AIVideoCreationContainer(props) {
  return (
    <VideoCreationContainer
      title="AI Video Creator 🎬"
      description="Create engaging videos with AI-generated scripts, professional voiceovers, and dynamic visuals."
      videoType="AI Video"
      {...props}
    />
  );
}

export function MemeVideoCreationContainer(props) {
  return (
    <VideoCreationContainer
      title="Meme Video Creator 😂"
      description="Transform videos into viral memes with custom text, styling, and effects."
      videoType="Meme Video"
      {...props}
    />
  );
}

export function PodcastClipperContainer(props) {
  return (
    <VideoCreationContainer
      title="Podcast Clipper ✂️"
      description="Extract engaging clips from podcasts with AI-powered content analysis."
      videoType="Podcast Clip"
      {...props}
    />
  );
}

export function UGCVideoCreationContainer(props) {
  return (
    <VideoCreationContainer
      title="AI UGC Video Creator 🎬"
      description="Create authentic UGC-style videos with AI creators speaking over your custom backgrounds."
      videoType="UGC Video"
      {...props}
    />
  );
}
