/**
 * GenerationButton Atom
 * Reusable button component for video generation with consistent styling and behavior
 */

import React from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, Sparkles } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {boolean} props.isGenerating - Whether generation is in progress
 * @param {boolean} props.disabled - Whether button is disabled
 * @param {string} props.generationMessage - Message to show during generation
 * @param {string} props.defaultText - Default button text
 * @param {Function} props.onClick - Click handler
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.size - Button size variant
 * @param {string} props.variant - Button style variant
 * @param {React.ReactNode} props.icon - Custom icon (defaults to Sparkles)
 * @param {string} props.title - Tooltip text
 */
export default function GenerationButton({
  isGenerating = false,
  disabled = false,
  generationMessage = "Generating...",
  defaultText = "Generate Video",
  onClick,
  className,
  size = "lg",
  variant = "default",
  icon: CustomIcon = Sparkles,
  title,
  ...props
}) {
  const isDisabled = disabled || isGenerating;
  
  return (
    <Button
      onClick={onClick}
      disabled={isDisabled}
      className={cn("w-full", className)}
      size={size}
      variant={variant}
      title={title}
      {...props}
    >
      {isGenerating ? (
        <Loader2 className="mr-2 h-5 w-5 animate-spin" />
      ) : (
        <CustomIcon className="mr-2 h-5 w-5" />
      )}
      {isGenerating ? generationMessage : defaultText}
    </Button>
  );
}

/**
 * Specialized generation button variants
 */

export function AIVideoGenerationButton(props) {
  return (
    <GenerationButton
      defaultText="Generate AI Video"
      generationMessage="Creating AI Video..."
      {...props}
    />
  );
}

export function MemeVideoGenerationButton(props) {
  return (
    <GenerationButton
      defaultText="Generate Meme Video"
      generationMessage="Creating Meme Video..."
      {...props}
    />
  );
}

export function PodcastClipperButton(props) {
  return (
    <GenerationButton
      defaultText="Clip Podcast"
      generationMessage="Processing Podcast..."
      {...props}
    />
  );
}

export function UGCVideoGenerationButton(props) {
  return (
    <GenerationButton
      defaultText="Generate UGC Video"
      generationMessage="Creating UGC Video..."
      {...props}
    />
  );
}
