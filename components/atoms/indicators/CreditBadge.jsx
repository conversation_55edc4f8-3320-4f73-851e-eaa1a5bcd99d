/**
 * CreditBadge Atom
 * Displays user credit information with status indicators
 */

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Coins, AlertTriangle, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {number} props.credits - Current credit balance
 * @param {number} props.requiredCredits - Credits required for action
 * @param {boolean} props.showIcon - Whether to show credit icon
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.variant - Badge variant
 * @param {boolean} props.showStatus - Whether to show status indicator
 */
export default function CreditBadge({
  credits = 0,
  requiredCredits = 0,
  showIcon = true,
  className,
  variant,
  showStatus = true,
  ...props
}) {
  const hasSufficientCredits = credits >= requiredCredits;
  
  // Determine badge variant based on credit status
  const badgeVariant = variant || (
    hasSufficientCredits ? 'default' : 'destructive'
  );
  
  // Status icon
  const StatusIcon = hasSufficientCredits ? CheckCircle : AlertTriangle;
  
  return (
    <Badge
      variant={badgeVariant}
      className={cn(
        "flex items-center gap-1.5",
        className
      )}
      {...props}
    >
      {showIcon && <Coins className="h-3 w-3" />}
      <span>{credits} credits</span>
      {showStatus && requiredCredits > 0 && (
        <StatusIcon className="h-3 w-3" />
      )}
    </Badge>
  );
}

/**
 * Credit status variants
 */

export function CreditStatusBadge({ credits, requiredCredits, ...props }) {
  const hasSufficientCredits = credits >= requiredCredits;
  
  return (
    <CreditBadge
      credits={credits}
      requiredCredits={requiredCredits}
      variant={hasSufficientCredits ? 'default' : 'destructive'}
      showStatus={true}
      {...props}
    />
  );
}

export function SimpleCreditBadge({ credits, ...props }) {
  return (
    <CreditBadge
      credits={credits}
      showStatus={false}
      variant="secondary"
      {...props}
    />
  );
}
