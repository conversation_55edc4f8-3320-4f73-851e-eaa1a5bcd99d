/**
 * Meme Video Page - Migrated to Atomic Design
 * Demonstrates reusability of VideoCreationLayout and atomic components
 */

import React, { useContext } from 'react';
import { UserDetailContext } from '@/context/UserDetailContext';
import { MemeVideoCreationLayout } from '@/components/templates/VideoCreationLayout';
import MemeVideoConfigurationForm from '@/components/organisms/forms/MemeVideoConfigurationForm';
import { useMemeVideoForm } from '@/hooks/useMemeVideoForm';
import { triggerMemeVideoGeneration } from '@/actions/memeVideoGeneration';
import { generatePreviewSections } from '@/lib/videoCreationUtils';

/**
 * Meme Video Creation Page Component
 * Shows how the same template and atomic components work across different video types
 */
export default function MemeVideoPage({ initialCredits = 0 }) {
  const { userDetail } = useContext(UserDetailContext);
  
  // Use the meme video form hook (follows same pattern as AI Video)
  const {
    formData,
    errors,
    updateField,
    validateForm,
    videoGeneration,
    isValid,
    estimatedDuration
  } = useMemeVideoForm();

  // Handle form field changes
  const handleFormChange = (field, value) => {
    updateField(field, value);
  };

  // Generate preview sections for the preview panel
  const previewSections = generatePreviewSections('Meme Video', {
    ...formData,
    userCredits: userDetail?.credits || initialCredits
  });

  // Enhanced preview sections with meme-specific data
  const enhancedPreviewSections = [
    ...previewSections,
    ...(formData.memeText ? [{
      icon: 'Type',
      title: 'Meme Text',
      content: formData.memeText,
      badge: `${formData.memeText.length}/200`
    }] : []),
    ...(formData.font ? [{
      icon: 'Palette',
      title: 'Font Style',
      content: `${formData.font} (${formData.fontSize}px)`,
      badge: formData.textColor
    }] : []),
    ...(estimatedDuration ? [{
      icon: 'Clock',
      title: 'Video Duration',
      content: `${estimatedDuration} seconds`,
      badge: 'Estimated'
    }] : [])
  ];

  // Configuration form component
  const configurationForm = (
    <MemeVideoConfigurationForm
      formData={formData}
      onFieldChange={handleFormChange}
      errors={errors}
      videoGeneration={videoGeneration}
    />
  );

  return (
    <MemeVideoCreationLayout
      generationAction={triggerMemeVideoGeneration}
      validateForm={() => validateForm(formData)}
      configurationForm={configurationForm}
      previewSections={enhancedPreviewSections}
      formData={{
        ...formData,
        userCredits: userDetail?.credits || initialCredits
      }}
      onFormChange={handleFormChange}
      creditCost={videoGeneration.creditCost}
    />
  );
}

/**
 * Meme Video Client Component
 * Wrapper that provides the main page (same pattern as AI Video)
 */
export function MemeVideoClient({ initialCredits }) {
  return <MemeVideoPage initialCredits={initialCredits} />;
}
