/**
 * AI Video Page - Migrated to Atomic Design
 * Uses the new VideoCreationLayout template and atomic components
 */

import React, { useContext } from 'react';
import { UserDetailContext } from '@/context/UserDetailContext';
import { AIVideoCreationLayout } from '@/components/templates/VideoCreationLayout';
import AIVideoConfigurationForm from '@/components/organisms/forms/AIVideoConfigurationForm';
import { useAIVideoForm } from '@/hooks/useAIVideoForm';
import { triggerAIVideoGeneration } from '@/actions/aiVideoGeneration';
import { generatePreviewSections } from '@/lib/videoCreationUtils';

/**
 * AI Video Creation Page Component
 * Migrated to use Atomic Design principles while maintaining all functionality
 */
export default function AIVideoPage({ initialCredits = 0 }) {
  const { userDetail } = useContext(UserDetailContext);
  
  // Use the migrated AI Video form hook
  const {
    formData,
    errors,
    updateField,
    validateForm,
    videoGeneration,
    isValid
  } = useAIVideoForm();

  // Handle form field changes
  const handleFormChange = (field, value) => {
    updateField(field, value);
  };

  // Generate preview sections for the preview panel
  const previewSections = generatePreviewSections('AI Video', {
    ...formData,
    userCredits: userDetail?.credits || initialCredits
  });

  // Enhanced preview sections with AI Video specific data
  const enhancedPreviewSections = [
    ...previewSections,
    ...(formData.estimatedDuration ? [{
      icon: 'Clock',
      title: 'Estimated Duration',
      content: `${Math.floor(formData.estimatedDuration / 60)}:${(formData.estimatedDuration % 60).toString().padStart(2, '0')}`,
      badge: 'Estimated'
    }] : [])
  ];

  // Configuration form component
  const configurationForm = (
    <AIVideoConfigurationForm
      formData={formData}
      onFieldChange={handleFormChange}
      errors={errors}
      videoGeneration={videoGeneration}
    />
  );

  return (
    <AIVideoCreationLayout
      generationAction={triggerAIVideoGeneration}
      validateForm={() => validateForm(formData)}
      configurationForm={configurationForm}
      previewSections={enhancedPreviewSections}
      formData={{
        ...formData,
        userCredits: userDetail?.credits || initialCredits
      }}
      onFormChange={handleFormChange}
      creditCost={videoGeneration.creditCost}
    />
  );
}

/**
 * AI Video Client Component
 * Wrapper that handles authentication and provides the main page
 */
export function AIVideoClient({ initialCredits }) {
  return <AIVideoPage initialCredits={initialCredits} />;
}
