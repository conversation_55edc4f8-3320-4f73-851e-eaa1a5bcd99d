/**
 * VideoCreationLayout Template
 * Standard layout template for all video creation pages
 */

import React from 'react';
import VideoCreationContainer from '@/components/organisms/VideoCreationContainer';
import VideoPreviewPanel from '@/components/organisms/VideoPreviewPanel';
import { cn } from '@/lib/utils';

/**
 * @param {Object} props
 * @param {string} props.title - Page title
 * @param {string} props.description - Page description
 * @param {string} props.videoType - Type of video being created
 * @param {Function} props.generationAction - Server action for generation
 * @param {Function} props.validateForm - Form validation function
 * @param {React.ReactNode} props.configurationForm - Form component
 * @param {Array} props.previewSections - Custom preview sections
 * @param {Object} props.formData - Current form data
 * @param {Function} props.onFormChange - Form change handler
 * @param {number} props.creditCost - Cost in credits
 * @param {string} props.className - Additional CSS classes
 */
export default function VideoCreationLayout({
  title,
  description,
  videoType,
  generationAction,
  validateForm,
  configurationForm,
  previewSections = [],
  formData = {},
  onFormChange,
  creditCost,
  className,
  children,
  ...props
}) {
  // Enhanced configuration panel with form change handling
  const enhancedConfigurationForm = React.cloneElement(configurationForm, {
    formData,
    onChange: onFormChange,
    ...configurationForm.props
  });

  // Preview panel with video-specific sections
  const previewPanel = (
    <VideoPreviewPanel
      formData={formData}
      videoType={videoType}
      previewSections={previewSections}
    />
  );

  return (
    <VideoCreationContainer
      title={title}
      description={description}
      videoType={videoType}
      generationAction={generationAction}
      validateForm={validateForm}
      configurationPanel={enhancedConfigurationForm}
      previewPanel={previewPanel}
      formData={formData}
      creditCost={creditCost}
      className={className}
      {...props}
    >
      {children}
    </VideoCreationContainer>
  );
}

/**
 * Specialized layout templates for different video types
 */

export function AIVideoCreationLayout(props) {
  return (
    <VideoCreationLayout
      title="AI Video Creator 🎬"
      description="Create engaging videos with AI-generated scripts, professional voiceovers, and dynamic visuals."
      videoType="AI Video"
      {...props}
    />
  );
}

export function MemeVideoCreationLayout(props) {
  return (
    <VideoCreationLayout
      title="Meme Video Creator 😂"
      description="Transform videos into viral memes with custom text, styling, and effects."
      videoType="Meme Video"
      {...props}
    />
  );
}

export function PodcastClipperLayout(props) {
  return (
    <VideoCreationLayout
      title="Podcast Clipper ✂️"
      description="Extract engaging clips from podcasts with AI-powered content analysis."
      videoType="Podcast Clip"
      {...props}
    />
  );
}

export function UGCVideoCreationLayout(props) {
  return (
    <VideoCreationLayout
      title="AI UGC Video Creator 🎬"
      description="Create authentic UGC-style videos with AI creators speaking over your custom backgrounds."
      videoType="UGC Video"
      {...props}
    />
  );
}
